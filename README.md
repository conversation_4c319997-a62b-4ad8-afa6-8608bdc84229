# Argus

**Argus** is a high-performance, Go-based SNMP trap receiver that translates cryptic SNMP notifications into human-readable alerts. Argus receives SNMP trap messages, translates OIDs to human-readable names using a Go wrapper around the `snmptranslate` command-line tool with local MIB files, and outputs structured, readable messages. It serves as a bridge between network monitoring systems and human operators, supporting flexible output and advanced templating.

---

## Features

- **SNMP Trap Reception**: Listens for incoming SNMP trap messages on a configurable port
- **OID Translation**: Uses a Go wrapper around the `snmptranslate` command-line tool to convert Object Identifiers to human-readable names using local MIB files
- **Message Processing**: Transforms raw SNMP data into structured, readable output
- **Configurable Output**: Flexible message formatting using the Cue templating language
- **Multiple Output Destinations**: STDOUT (logfmt/JSON), file, and Elasticsearch
- **High Performance**: Built in Go for efficient concurrent processing
- **Hot Reloading**: Configuration changes are detected and applied without a restart
- **Filtering & Routing**: Filter and route traps based on content
- **Secure Credentials**: Encrypted storage of SNMP credentials
- **Metrics**: Performance and operational metrics
- **Containerized**: Ready to deploy with Docker

---

## Architecture

### High-Level Overview

```mermaid
graph TD
    A[Network Device] -->|SNMP Trap| B[Argus Listener]
    B --> C[Trap Parser]
    C --> D[OID Translator]
    D -->|snmptranslate wrapper| E[Local MIB Files]
    D --> F[Cue Template Engine]
    F --> G[Output Handler]
    G --> H[STDOUT - logfmt/JSON]
    G --> I[Elasticsearch]
```

### Detailed Component Architecture

```mermaid
graph TB
    subgraph "Network Layer"
        ND1[Router]
        ND2[Switch]
        ND3[Server]
        ND4[Firewall]
    end

    subgraph "Argus Application"
        subgraph "Input Layer"
            SL[SNMP Listener :162]
            TP[Trap Parser]
        end

        subgraph "Processing Layer"
            OT[OID Translator<br/>snmptranslate wrapper]
            MIB[(Local MIB Files)]
            TE[Template Engine]
            TC[Template Cache]
        end

        subgraph "Template System"
            CT[Cue Templates]
            DT[Default Template]
            ST[Specific Templates]
        end

        subgraph "Output Layer"
            OH[Output Handler]
            SF[Stdout Formatter]
            EF[Elasticsearch Formatter]
        end
    end

    subgraph "Output Destinations"
        STDOUT[STDOUT<br/>logfmt/JSON]
        ES[Elasticsearch<br/>Index]
    end

    ND1 -->|SNMP Trap| SL
    ND2 -->|SNMP Trap| SL
    ND3 -->|SNMP Trap| SL
    ND4 -->|SNMP Trap| SL

    SL --> TP
    TP --> OT
    OT --> MIB
    OT --> TE
    TE <--> TC
    TE --> CT
    CT --> DT
    CT --> ST
    TE --> OH
    OH --> SF
    OH --> EF
    SF --> STDOUT
    EF --> ES
```

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant ND as Network Device
    participant AL as Argus Listener
    participant TP as Trap Parser
    participant OT as OID Translator
    participant MIB as MIBs Files
    participant TE as Template Engine
    participant TC as Template Cache
    participant OH as Output Handler
    participant OUT as Output Destination

    ND->>AL: SNMP Trap (UDP:162)
    AL->>TP: Raw trap data
    TP->>TP: Parse trap structure
    TP->>OT: Parsed OIDs + values
    OT->>MIB: snmptranslate command with MIB paths
    MIB-->>OT: Human-readable names
    OT->>TE: Translated data

    alt Template in cache
        TE->>TC: Get template
        TC-->>TE: Cached template
    else Template not cached
        TE->>TC: Load template from disk
        TC-->>TE: Fresh template
        TE->>TC: Cache template
    end

    TE->>TE: Apply Cue template
    TE->>OH: Formatted message

    par STDOUT Output
        OH->>OUT: logfmt/JSON to STDOUT
    and Elasticsearch Output
        OH->>OUT: JSON to Elasticsearch
    end
```

---

## Quick Start

### Prerequisites

- Go 1.24 or higher
- **snmptranslate command-line tool** (from net-snmp package) - **Required for OID translation**
  - Ubuntu/Debian: `sudo apt-get install snmp snmp-mibs-downloader`
  - RHEL/CentOS: `sudo yum install net-snmp-utils`
  - macOS: `brew install net-snmp`
  - Verify installation: `which snmptranslate`
- **MIB files** for OID translation (typically in `/usr/share/snmp/mibs`)
  - Standard MIBs are included with net-snmp installation
  - Additional enterprise MIBs can be placed in custom directories
- Docker (optional, for containerized deployment)

> **Note**: Argus uses a Go wrapper around the `snmptranslate` command-line tool for OID translation. The `snmptranslate` binary must be available in the system PATH for proper OID-to-name translation functionality.

### Build & Run

```bash
# Clone the repository
git clone <repository-url>
cd argus
go mod download
go build -o argus

# Run the application with default settings
./argus

# Run with a configuration file
./argus --config=config/config.yml

# Run on a non-privileged port (recommended for local testing)
./argus --port=1162

# Show help information
./argus --help
```

### Docker Usage

```bash
docker build -t argus .
docker run -p 162:162/udp argus
```

---

## Configuration

Argus can be configured using command-line flags, a YAML configuration file, or environment variables. The application uses the Cobra command-line framework and Viper configuration management for a robust and flexible configuration system.

> **Note:** Argus has been migrated to use the Cobra/Viper framework. See [Cobra/Viper Migration](docs/cobra-migration.md) for details.

### Command-Line Flags

Argus uses the Cobra command-line framework, which provides improved help output and error handling:

```bash
# Show help information
argus --help

# Show version information
argus --version

# Validate configuration without starting the application
argus validate

# Validate a specific configuration file
argus validate --config=/path/to/config.yml
```

| Flag          | Type     | Description                                                      | Default                |
|---------------|----------|------------------------------------------------------------------|------------------------|
| `--config`    | `string` | Path to configuration file                                       | `config/minimal-config.yml`   |
| `--port`      | `int`    | UDP port to listen for SNMP traps (standard: 162, requires root) | `0` (use config file)  |
| `--mib-paths` | `string` | Comma-separated list of directories containing MIB files for snmptranslate | `""` (use config file) |
| `--output`    | `string` | Output type: `stdout`, `elasticsearch`, `both`                   | `""` (use config file) |
| `--format`    | `string` | Output format: `text`, `json`, `logfmt`                          | `""` (use config file) |
| `--debug`     | `bool`   | Enable debug logging                                             | `false`                |
| `--version`   | `bool`   | Show version information                                         | `false`                |

> **Note:** Port 162 is a privileged port and requires root permissions. For local testing, use a non-privileged port such as 1162: `./argus --port=1162`.

### Configuration File (YAML)

By default, Argus looks for a configuration file at `config/minimal-config.yml`. If not found, built-in defaults and environment variable overrides are used.

Example:

```yaml
server:
  port: 162
  bind_address: "0.0.0.0"

snmp:
  community: "public"
  version: "2c"
  mib_paths:
    - "/usr/share/snmp/mibs"     # System-wide standard MIBs
    - "./mibs"                   # Local project MIBs
    - "/opt/mibs"                # Additional MIB directory
    - "/opt/enterprise-mibs"     # Enterprise-specific MIBs

output:
  stdout:
    enabled: true
    format: "logfmt"  # or "json"
  elasticsearch:
    enabled: false
    url: "https://elasticsearch.example.com:9200"
    index: "argus-traps"

templates:
  path: "./templates"
  default: "default.cue"

logging:
  level: "info"
  file: "/var/log/argus/argus.log"
```

### Environment Variables

You can override configuration values using environment variables with the format `ARGUS_<SECTION>_<OPTION>`:

```bash
export ARGUS_SNMP_PORT=1162
export ARGUS_LOG_LEVEL=debug
export ARGUS_SNMP_V1V2C_COMMUNITY=secret
```

---

## OID Translation & MIB Management

Argus uses a Go wrapper around the `snmptranslate` command-line tool to convert numeric OIDs to human-readable names. This approach leverages the mature MIB parsing capabilities of net-snmp while maintaining the performance and reliability of a Go application.

### How It Works

The OID translation process follows these steps:

1. **Trap Reception**: SNMP trap contains numeric OIDs (e.g., `*******.2.1.2.2.1.8.1`)
2. **Translation Request**: Go wrapper calls `snmptranslate` with configured MIB paths
3. **MIB Lookup**: `snmptranslate` searches MIB files for OID definitions
4. **Name Resolution**: Returns human-readable name (e.g., `ifOperStatus.1`)
5. **Fallback**: If translation fails, original OID is preserved

### MIB Configuration

Configure MIB search paths in order of precedence:

```yaml
snmp:
  mib_paths:
    - "/usr/share/snmp/mibs"     # System MIBs
    - "./mibs"                   # Local MIBs
    - "/opt/enterprise-mibs"     # Enterprise MIBs
```

### Error Handling

The wrapper handles common error scenarios:

- **Missing MIBs**: Returns original OID with warning log
- **Invalid OID**: Logs error and continues processing
- **snmptranslate unavailable**: Falls back to basic OID formatting
- **Timeout**: Configurable timeout prevents hanging

### Troubleshooting OID Translation

**Common Issues and Solutions**:

1. **snmptranslate not found**:

   ```bash
   # Check if snmptranslate is installed
   which snmptranslate

   # Install if missing (Ubuntu/Debian)
   sudo apt-get install snmp snmp-mibs-downloader
   ```

2. **MIB files not found**:

   ```bash
   # Check MIB directory exists and has files
   ls -la /usr/share/snmp/mibs/

   # Test snmptranslate directly
   snmptranslate -m ALL -M /usr/share/snmp/mibs -OS *******.2.1.1.1.0
   ```

3. **Permission issues**:

   ```bash
   # Ensure MIB directories are readable
   chmod -R 755 /usr/share/snmp/mibs/
   ```

4. **Custom MIB integration**:

   ```yaml
   # Add custom MIB paths to configuration
   snmp:
     mib_paths:
       - "/usr/share/snmp/mibs"
       - "./custom-mibs"
       - "/opt/enterprise-mibs"
   ```

### Go Wrapper Implementation

The `internal/translator` package provides a Go wrapper around the `snmptranslate` command:

```go
// Example from internal/translator package
import (
    "os/exec"
    "strings"
    "time"
)

type OIDTranslator struct {
    mibPaths []string
    timeout  time.Duration
    cache    *lru.Cache
}

func (t *OIDTranslator) TranslateOID(oid string) (string, error) {
    // Check cache first
    if cached, found := t.cache.Get(oid); found {
        return cached.(string), nil
    }

    // Execute snmptranslate command
    cmd := exec.Command("snmptranslate",
        "-m", "ALL",                              // Load all MIBs
        "-M", strings.Join(t.mibPaths, ":"),     // MIB search paths
        "-OS",                                    // Symbolic output format
        oid)                                      // OID to translate

    output, err := cmd.Output()
    if err != nil {
        return oid, err // Fallback to original OID
    }

    name := strings.TrimSpace(string(output))
    t.cache.Add(oid, name) // Cache the result
    return name, nil
}

// Usage example
translator := NewOIDTranslator(config.SNMP.MIBPaths, 5*time.Second)
name, err := translator.TranslateOID("*******.2.1.2.2.1.8.1")
if err != nil {
    slog.Warn("OID translation failed", "error", err, "oid", oid)
    name = oid // Fallback to original OID
}
// name = "ifOperStatus.1"
```

### Command Execution Details

The wrapper executes commands in the following format:

```bash
snmptranslate -m ALL -M /usr/share/snmp/mibs:./mibs:custom/mibs -OS *******.2.1.2.2.1.8.1
```

Where:

- `-m ALL` loads all available MIBs
- `-M <paths>` specifies colon-separated MIB search directories
- `-OS` requests symbolic output format (human-readable names)
- The final argument is the OID to translate

---

## Usage

### Basic

```bash
# Start Argus with default configuration
./argus

# Start with custom configuration file
./argus -config /path/to/config.yaml

# Start with specific port
./argus -port 1162
```

### Docker

```bash
# Run with mounted configuration
docker run -v /path/to/config.yaml:/app/config.yaml -p 162:162/udp argus

# Run with environment variables
docker run -e ARGUS_PORT=162 -e ARGUS_COMMUNITY=public -p 162:162/udp argus
```

---

## Output Format & Templating

Argus uses the Cue templating language to transform SNMP traps into structured messages. Templates are defined per trap type for flexible formatting.

### Input (Raw SNMP Trap)

```text
SNMPv2-MIB::sysUpTime.0 = Timeticks: (123456) 0:20:34.56
SNMPv2-MIB::snmpTrapOID.0 = OID: IF-MIB::linkDown
IF-MIB::ifIndex.1 = INTEGER: 1
IF-MIB::ifAdminStatus.1 = INTEGER: up(1)
IF-MIB::ifOperStatus.1 = INTEGER: down(2)
```

### Cue Template Example

```cue
package templates

linkDown: {
    timestamp: string
    source_ip: string
    trap_type: "linkDown"
    severity: "warning"
    message: "Interface \(interface_index) is down (admin status: \(admin_status), operational status: \(operational_status))"
    details: {
        system_uptime: string
        interface_index: int
        admin_status: string
        operational_status: string
    }
}
```

### Output Formats

#### JSON Format

```json
{
  "timestamp": "2024-01-15T10:30:45Z",
  "source_ip": "*************",
  "trap_type": "linkDown",
  "severity": "warning",
  "message": "Interface 1 is down (admin status: up, operational status: down)",
  "details": {
    "system_uptime": "0:20:34.56",
    "interface_index": 1,
    "admin_status": "up",
    "operational_status": "down"
  }
}
```

#### Logfmt Format

```text
timestamp=2024-01-15T10:30:45Z source_ip=************* trap_type=linkDown severity=warning message="Interface 1 is down (admin status: up, operational status: down)" system_uptime="0:20:34.56" interface_index=1 admin_status=up operational_status=down
```

---

## Testing & Development

### Sending Test Traps

You can test Argus by sending SNMP traps using the `snmptrap` command:

```bash
snmptrap -v 2c -c public localhost:1162 '' *******.4.1.8072.******* *******.*******.0 i 123456
```

Or use the provided test tool:

```bash
go run tools/test_trap.go
```

---

## Development

### Project Structure

```text
argus/
├── main.go              # Main application entry point
├── internal/
│   ├── config/          # Configuration handling
│   ├── snmp/            # SNMP trap handling
│   ├── translator/      # OID translation using snmptranslate wrapper
│   ├── formatter/       # Message formatting using Cue templates
│   └── output/          # Output handlers (STDOUT, Elasticsearch)
├── templates/           # Cue template definitions
├── mibs/                # Local MIB files
├── configs/             # Example configurations
├── docs/                # Documentation
└── tests/               # Test files
```

> **Note:** The above structure follows Go project best practices, separating concerns into clear internal packages and top-level directories for configuration, templates, documentation, and tests.

### Building

```bash
# Build for current platform
go build -o argus .

# Build for multiple platforms
make build-all

# Run tests
go test ./...

# Run with race detection
go test -race ./...
```

### Static Analysis

The project includes comprehensive static analysis tools for code quality, security, and maintainability:

```bash
# Install all static analysis tools
make install-tools

# Run comprehensive analysis
make analysis

# Run individual tools
make lint          # golangci-lint
make security      # gosec + govulncheck
make staticcheck   # advanced static analysis
make ineffassign   # ineffectual assignments
make gocyclo       # cyclomatic complexity
make dupl          # code duplication

# Use the comprehensive script
./scripts/static-analysis.sh
```

**Available Tools:**

- **golangci-lint** - 20+ linters for code quality
- **gosec** - Security vulnerability scanner
- **govulncheck** - Go vulnerability database scanner
- **staticcheck** - Advanced static analysis
- **ineffassign** - Ineffectual assignment detection
- **gocyclo** - Cyclomatic complexity analysis
- **dupl** - Code duplication detection

Reports are generated in the `reports/` directory. See [docs/static-analysis.md](docs/static-analysis.md) for detailed documentation.
