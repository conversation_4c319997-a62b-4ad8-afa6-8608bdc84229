# Argus

**Argus** is a high-performance, Go-based SNMP trap receiver that transforms cryptic SNMP notifications into human-readable alerts. Argus receives SNMP trap messages, translates OIDs to meaningful names using a comprehensive fallback system and Go wrapper around the `snmptranslate` command-line tool, and outputs structured, readable messages in logfmt or JSON format. It serves as a bridge between network monitoring systems and human operators, providing immediate visibility into network events with zero configuration required.

---

## Features

- **SNMP Trap Reception**: Listens for incoming SNMP trap messages on a configurable port
- **Intelligent OID Translation**: Comprehensive fallback system with 50+ standard IETF MIB OIDs, plus Go wrapper around `snmptranslate` for extended MIB support
- **Human-Readable Output**: Transforms cryptic OIDs like `*******.*******.5.3` into meaningful names like `linkDown`
- **Zero Configuration**: Works out-of-the-box with automatic default logfmt output when no outputs are configured
- **Multiple Output Formats**: STDOUT (logfmt/JSON), Elasticsearch, with structured logging using <PERSON>'s native slog
- **High Performance**: Built in Go with intelligent caching, concurrent processing, and fast fallback mechanisms
- **Hot Reloading**: Configuration changes are detected and applied without a restart
- **Filtering & Routing**: Filter and route traps based on content with automatic fallback to all outputs
- **Template Engine**: Flexible message formatting using the Cue templating language
- **Production Ready**: Comprehensive error handling, graceful degradation, and robust fallback mechanisms
- **Containerized**: Ready to deploy with Docker

---

## Architecture

### High-Level Overview

```mermaid
graph TD
    A[Network Device] -->|SNMP Trap| B[Argus Listener]
    B --> C[Trap Parser]
    C --> D[OID Translator]
    D -->|Fallback System| E[50+ Standard OIDs]
    D -->|snmptranslate wrapper| F[Local MIB Files]
    D --> G[Template Engine / Default Output]
    G --> H[Output Handler]
    H --> I[STDOUT - logfmt/JSON]
    H --> J[Elasticsearch]
```

### Detailed Component Architecture

```mermaid
graph TB
    subgraph "Network Layer"
        ND1[Router]
        ND2[Switch]
        ND3[Server]
        ND4[Firewall]
    end

    subgraph "Argus Application"
        subgraph "Input Layer"
            SL[SNMP Listener :162]
            TP[Trap Parser]
        end

        subgraph "Processing Layer"
            OT[OID Translator<br/>Fallback + snmptranslate]
            FB[Fallback System<br/>50+ Standard OIDs]
            MIB[(Local MIB Files)]
            TE[Template Engine]
            TC[Template Cache]
            DO[Default Output<br/>Auto logfmt]
        end

        subgraph "Template System"
            CT[Cue Templates]
            DT[Default Template]
            ST[Specific Templates]
        end

        subgraph "Output Layer"
            OH[Output Handler]
            SF[Stdout Formatter]
            EF[Elasticsearch Formatter]
        end
    end

    subgraph "Output Destinations"
        STDOUT[STDOUT<br/>logfmt/JSON]
        ES[Elasticsearch<br/>Index]
    end

    ND1 -->|SNMP Trap| SL
    ND2 -->|SNMP Trap| SL
    ND3 -->|SNMP Trap| SL
    ND4 -->|SNMP Trap| SL

    SL --> TP
    TP --> OT
    OT --> FB
    OT --> MIB
    OT --> TE
    OT --> DO
    TE <--> TC
    TE --> CT
    CT --> DT
    CT --> ST
    TE --> OH
    DO --> OH
    OH --> SF
    OH --> EF
    SF --> STDOUT
    EF --> ES
```

### Data Flow Sequence

```mermaid
sequenceDiagram
    participant ND as Network Device
    participant AL as Argus Listener
    participant TP as Trap Parser
    participant OT as OID Translator
    participant MIB as MIBs Files
    participant TE as Template Engine
    participant TC as Template Cache
    participant OH as Output Handler
    participant OUT as Output Destination

    ND->>AL: SNMP Trap (UDP:162)
    AL->>TP: Raw trap data
    TP->>TP: Parse trap structure
    TP->>OT: Parsed OIDs + values

    alt OID in fallback system
        OT->>OT: Use 50+ standard OID translations
    else OID not in fallback
        OT->>MIB: snmptranslate command with MIB paths
        MIB-->>OT: Human-readable names
    end

    OT->>TE: Translated data

    alt Template in cache
        TE->>TC: Get template
        TC-->>TE: Cached template
    else Template not cached
        TE->>TC: Load template from disk
        TC-->>TE: Fresh template
        TE->>TC: Cache template
    end

    TE->>TE: Apply Cue template
    TE->>OH: Formatted message

    par STDOUT Output
        OH->>OUT: logfmt/JSON to STDOUT
    and Elasticsearch Output
        OH->>OUT: JSON to Elasticsearch
    end
```

---

## Quick Start

### Prerequisites

- Go 1.24 or higher
- **snmptranslate command-line tool** (from net-snmp package) - **Required for OID translation**
  - Ubuntu/Debian: `sudo apt-get install snmp snmp-mibs-downloader`
  - RHEL/CentOS: `sudo yum install net-snmp-utils`
  - macOS: `brew install net-snmp`
  - Verify installation: `which snmptranslate`
- **MIB files** for OID translation (typically in `/usr/share/snmp/mibs`)
  - Standard MIBs are included with net-snmp installation
  - Additional enterprise MIBs can be placed in custom directories
- Docker (optional, for containerized deployment)

> **Note**: Argus uses a Go wrapper around the `snmptranslate` command-line tool for OID translation. The `snmptranslate` binary must be available in the system PATH for proper OID-to-name translation functionality.

### Build & Run

```bash
# Clone the repository
<NAME_EMAIL>:stel/infra/supervision/argus/argus-app.git
cd argus
go mod download
go build -o argus

# Run the application with default settings
./argus

# Run with a configuration file
./argus --config=config/config.yml

# Run on a non-privileged port (recommended for local testing)
./argus --port=1162

# Show help information
./argus --help
```

### Docker Usage

```bash
docker build -t argus .
docker run -p 162:162/udp argus
```

---

## Configuration

Argus can be configured using command-line flags, a YAML configuration file, or environment variables. The application uses the Cobra command-line framework and Viper configuration management for a robust and flexible configuration system.

### 

### Command-Line Flags

Argus uses the Cobra command-line framework, which provides improved help output and error handling:

```bash
# Show help information
argus --help

# Show version information
argus --version

# Validate configuration without starting the application
argus validate

# Validate a specific configuration file
argus validate --config=/path/to/config.yml
```

| Flag          | Type     | Description                                                      | Default                |
|---------------|----------|------------------------------------------------------------------|------------------------|
| long: `--config`, short: `-c`    | `string` | Path to configuration file                                       | `config/config.yml`   |
| long: `--port`, short: `-p`      | `int`    | UDP port to listen for SNMP traps (standard: 162, requires root) | `1162`  |
| long: `--mib-paths`, short: `-m` | `string` | Comma-separated list of directories containing MIB files for snmptranslate | `"mibs"` |
| long: `--output`, short: `-o`    | `string` | Output type: `stdout`, `elasticsearch`, ... (see config file for available options)                   | `"stdout"` |
| long: `--format`, short: `-c`    | `string` | Output format: `text`, `json`, `logfmt`                          | `"logfmt"` |
| long: `--debug`, short: `-d`     | `bool`   | Enable debug logging                                             | `false`                |
| long: `--version`, short: `-v`   | `bool`   | Show version information                                         | `false`                |

> **Note:** Port 162 is a privileged port and requires root permissions. For local testing, use a non-privileged port such as 1162: `./argus --port=1162`.

### Configuration File (YAML)

By default, Argus looks for a configuration file at `config/config.yml`. If not found, built-in defaults and environment variable overrides are used. **Argus works out-of-the-box with zero configuration** - when no outputs are configured, it automatically creates a default logfmt output to ensure trap messages are always visible.

Example:

```yaml
server:
  port: 162
  bind_address: "0.0.0.0"

snmp:
  community: "public"
  version: "2c"
  mib_paths:
    - "/usr/share/snmp/mibs"     # System-wide standard MIBs
    - "./mibs"                   # Local project MIBs
    - "/opt/mibs"                # Additional MIB directory
    - "/opt/enterprise-mibs"     # Enterprise-specific MIBs

output:
  stdout:
    enabled: true
    format: "logfmt"  # logfmt, json, or text
  elasticsearch:
    enabled: false
    url: "https://elasticsearch.example.com:9200"
    index: "argus-traps"

# Note: If no outputs are enabled, Argus automatically creates a default logfmt output

templates:
  path: "./templates"
  default: "default.cue"

logging:
  level: "info"
  file: "/var/log/argus/argus.log"
```

### Environment Variables

You can override configuration values using environment variables with the format `ARGUS_<SECTION>_<OPTION>`:

```bash
export ARGUS_SNMP_PORT=1162
export ARGUS_LOG_LEVEL=debug
export ARGUS_SNMP_V1V2C_COMMUNITY=secret
```

---

## OID Translation & MIB Management

Argus provides intelligent OID translation using a **comprehensive two-tier system**: a fast fallback mechanism with 50+ standard IETF MIB OIDs, plus a Go wrapper around the `snmptranslate` command-line tool for extended MIB support. This approach ensures reliable, human-readable output even when external tools are unavailable.

### How It Works

The OID translation process follows these steps:

1. **Trap Reception**: SNMP trap contains numeric OIDs (e.g., `*******.*******.5.3`)
2. **Fast Fallback Check**: Check comprehensive built-in database of 50+ standard OIDs
3. **External Translation**: If not in fallback, use Go wrapper to call `snmptranslate` with configured MIB paths
4. **Name Resolution**: Returns human-readable name (e.g., `linkDown`, `sysUpTime.0`, `ifOperStatus.1`)
5. **Graceful Fallback**: If all translation fails, original OID is preserved with warning

### Translation Examples

**Before (Raw OIDs)**:

```text
oid=.*******.*******.5.3 iso.3.6.1.*******.0="4h53m52.96s" iso.3.6.1.*******.4.1.0=.*******.*******.5.3
```

**After (Human-Readable)**:

```text
trap_oid=linkDown sysUpTime.0="4h53m52.96s" snmpTrapOID.0=.*******.*******.5.3 ifIndex=2
```

### MIB Configuration

Configure MIB search paths in order of precedence:

```yaml
snmp:
  mib_paths:
    - "/usr/share/snmp/mibs"     # System MIBs
    - "./mibs"                   # Local MIBs
    - "/opt/enterprise-mibs"     # Enterprise MIBs
```

### Built-in Fallback System

Argus includes a comprehensive fallback database with 50+ standard IETF MIB OIDs:

**System Group**: `sysDescr.0`, `sysUpTime.0`, `sysContact.0`, `sysName.0`, `sysLocation.0`
**Interface Group**: `ifIndex`, `ifDescr`, `ifType`, `ifAdminStatus`, `ifOperStatus`
**Standard Traps**: `coldStart`, `warmStart`, `linkDown`, `linkUp`, `authenticationFailure`
**SNMP Group**: `snmpTrapOID.0`, `snmpTrapEnterprise.0`

### Error Handling

The translation system handles common error scenarios gracefully:

- **Fast Fallback**: Uses built-in database for instant translation of standard OIDs
- **Missing MIBs**: Returns original OID with warning log
- **Invalid OID**: Logs error and continues processing
- **snmptranslate unavailable**: Falls back to built-in OID database
- **Timeout**: Configurable timeout prevents hanging (default: 5 seconds)
- **Multiple Strategies**: Tries 4 different snmptranslate approaches for best results

### Troubleshooting OID Translation

**Common Issues and Solutions**:

1. **snmptranslate not found**:

   ```bash
   # Check if snmptranslate is installed
   which snmptranslate

   # Install if missing (Ubuntu/Debian)
   sudo apt-get install snmp snmp-mibs-downloader
   ```

2. **MIB files not found**:

   ```bash
   # Check MIB directory exists and has files
   ls -la /usr/share/snmp/mibs/

   # Test snmptranslate directly
   snmptranslate -m ALL -M /usr/share/snmp/mibs -OS *******.*******.0
   ```

3. **Permission issues**:

   ```bash
   # Ensure MIB directories are readable
   chmod -R 755 /usr/share/snmp/mibs/
   ```

4. **Custom MIB integration**:

   ```yaml
   # Add custom MIB paths to configuration
   snmp:
     mib_paths:
       - "/usr/share/snmp/mibs"
       - "./custom-mibs"
       - "/opt/enterprise-mibs"
   ```

### Go Wrapper Implementation

The `internal/translator` package provides a comprehensive OID translation system:

```go
// Example from internal/translator package
import (
    "os/exec"
    "strings"
    "time"
)

type OIDTranslator struct {
    mibPaths []string
    timeout  time.Duration
    cache    map[string]string  // Simple map-based cache
}

func (t *OIDTranslator) TranslateOID(oid string) (string, error) {
    // 1. Check cache first for performance
    if cached, found := t.cache[oid]; found {
        return cached, nil
    }

    // 2. Try comprehensive fallback system (50+ standard OIDs)
    if fallback := t.getFallbackTranslation(oid); fallback != "" {
        t.cache[oid] = fallback
        return fallback, nil
    }

    // 3. Try multiple snmptranslate strategies
    strategies := [][]string{
        {oid},                                    // Default paths
        {"-OS", oid},                            // Symbolic output
        {"-m", "ALL", "-M", mibPaths, oid},      // With MIB paths
        {"-m", "ALL", "-M", mibPaths, "-OS", oid}, // Full options
    }

    for _, args := range strategies {
        cmd := exec.CommandContext(ctx, "snmptranslate", args...)
        if output, err := cmd.Output(); err == nil {
            name := strings.TrimSpace(string(output))
            if isValidTranslation(oid, name) {
                t.cache[oid] = name
                return name, nil
            }
        }
    }

    // 4. Return original OID if all strategies fail
    return oid, nil
}

// Usage example
translator := NewOIDTranslator(config.SNMP.MIBPaths, 5*time.Second)
name, _ := translator.TranslateOID("*******.*******.5.3")
// name = "linkDown" (from fallback system)
```

### Command Execution Details

The wrapper executes commands in the following format:

```bash
snmptranslate -m ALL -M /usr/share/snmp/mibs:./mibs:custom/mibs -OS *******.*******.1.8.1
```

Where:

- `-m ALL` loads all available MIBs
- `-M <paths>` specifies colon-separated MIB search directories
- `-OS` requests symbolic output format (human-readable names)
- The final argument is the OID to translate

---

## Usage

### Basic Usage

```bash
# Start Argus with zero configuration (uses automatic default logfmt output)
./argus

# Start with custom configuration file
./argus --config /path/to/config.yaml

# Start with specific port (recommended for testing)
./argus --port 1162

# Test with minimal configuration
./argus --config config/config.yml
```

### Example Output

When you send an SNMP trap, Argus automatically produces human-readable output:

```bash
# Send a test trap
snmptrap -v 2c -c public localhost:1162 '' *******.*******.5.3 *******.*******.1.1 i 2

# Argus output (automatic logfmt format)
time=2025-06-18T12:31:44.621+02:00 level=INFO msg="SNMP Trap" source=127.0.0.1 community=public version=SNMPv2c trap_type=SNMPv2Trap trap_oid=linkDown timestamp=2025-06-18T12:31:44.621+02:00 sysUpTime.0="4h53m52.96s (1763296)" snmpTrapOID.0=.*******.*******.5.3 ifIndex=2
```

### Docker

```bash
# Run with mounted configuration
docker run -v /path/to/config.yaml:/app/config.yaml -p 162:162/udp argus

# Run with environment variables
docker run -e ARGUS_PORT=162 -e ARGUS_COMMUNITY=public -p 162:162/udp argus
```

---

## Output Format & Templating

Argus uses the Cue templating language to transform SNMP traps into structured messages. Templates are defined per trap type for flexible formatting.

### Input (Raw SNMP Trap)

```text
# Raw SNMP trap data (what network devices send)
SNMPv2-MIB::sysUpTime.0 = Timeticks: (123456) 0:20:34.56
SNMPv2-MIB::snmpTrapOID.0 = OID: IF-MIB::linkDown
IF-MIB::ifIndex.1 = INTEGER: 1
IF-MIB::ifAdminStatus.1 = INTEGER: up(1)
IF-MIB::ifOperStatus.1 = INTEGER: down(2)
```

### Cue Template Example

```cue
package templates

linkDown: {
    timestamp: string
    source_ip: string
    trap_type: "linkDown"
    severity: "warning"
    message: "Interface \(interface_index) is down (admin status: \(admin_status), operational status: \(operational_status))"
    details: {
        system_uptime: string
        interface_index: int
        admin_status: string
        operational_status: string
    }
}
```

### Output Formats

#### JSON Format

```json
{
  "timestamp": "2024-01-15T10:30:45Z",
  "source_ip": "*************",
  "trap_type": "linkDown",
  "severity": "warning",
  "message": "Interface 1 is down (admin status: up, operational status: down)",
  "details": {
    "system_uptime": "0:20:34.56",
    "interface_index": 1,
    "admin_status": "up",
    "operational_status": "down"
  }
}
```

#### Logfmt Format (Default Output)

```text
# Automatic logfmt output (no configuration required)
time=2025-06-18T12:31:44.621+02:00 level=INFO msg="SNMP Trap" source=127.0.0.1 community=public version=SNMPv2c trap_type=SNMPv2Trap trap_oid=linkDown timestamp=2025-06-18T12:31:44.621+02:00 sysUpTime.0="4h53m52.96s (1763296)" snmpTrapOID.0=.*******.*******.5.3 ifIndex=2

# Template-based logfmt output (with custom formatting)
timestamp=2024-01-15T10:30:45Z source_ip=************* trap_type=linkDown severity=warning message="Interface 1 is down (admin status: up, operational status: down)" system_uptime="0:20:34.56" interface_index=1 admin_status=up operational_status=down
```

---

## Testing & Development

### Sending Test Traps

You can test Argus by sending SNMP traps using the `snmptrap` command:

```bash
# Test linkDown trap (will show as trap_oid=linkDown)
snmptrap -v 2c -c public localhost:1162 '' *******.*******.5.3 *******.*******.1.1 i 2

# Test linkUp trap (will show as trap_oid=linkUp)
snmptrap -v 2c -c public localhost:1162 '' *******.*******.5.4 *******.*******.1.1 i 3

# Test with custom enterprise OID
snmptrap -v 2c -c public localhost:1162 '' *******.4.1.8072.******* *******.*******.0 i 123456
```

Or use the provided test tool:

```bash
go run tools/test_trap.go
```

### Quick Test Setup

For immediate testing with zero configuration:

```bash
# Terminal 1: Start Argus (uses automatic default output)
go run . --config config/config.yml

# Terminal 2: Send test trap
snmptrap -v 2c -c public localhost:1162 '' *******.*******.5.3 *******.*******.1.1 i 2

# You'll immediately see human-readable output in Terminal 1
```

---

## Zero Configuration Mode

Argus is designed to work immediately without any configuration. This makes it perfect for quick testing, development, and environments where you just need to see SNMP traps.

### What Happens with Zero Configuration

1. **Automatic Default Output**: When no outputs are configured, Argus automatically creates a default logfmt output
2. **Intelligent OID Translation**: Uses built-in fallback system with 50+ standard OIDs for immediate human-readable output
3. **No Routes Required**: When no routing rules are configured, all traps are sent to all available outputs
4. **Graceful Degradation**: If external tools like `snmptranslate` are unavailable, the fallback system ensures traps are still readable

### Minimal Configuration Example

Create a minimal configuration file:

```yaml
# config/config.yml
server:
  port: 1162
  bind_address: "0.0.0.0"

snmp:
  community: "public"
  version: "2c"
  mib_paths:
    - "/usr/share/snmp/mibs"
    - "./mibs"

output:
  stdout:
    enabled: false  # Disable stdout to test default behavior
  elasticsearch:
    enabled: false

templates:
  path: "./templates"
  default: "default.cue"

logging:
  level: "debug"

health:
  enabled: true
  port: 8080
  bind_address: "0.0.0.0"
```

Run with this minimal configuration:

```bash
go run . --config config/config.yml
```

You'll see:

```text
time=2025-06-18T11:31:13.121+02:00 level=INFO msg="No outputs configured, adding default logfmt output"
time=2025-06-18T11:31:13.121+02:00 level=INFO msg="Default logfmt output handler initialized"
```

And when traps arrive, they're automatically formatted as human-readable logfmt output.

---

## Development

### Project Structure

```text
argus/
├── main.go              # Main application entry point
├── internal/
│   ├── config/          # Configuration handling with Viper
│   ├── snmp/            # SNMP trap handling, parsing, filtering
│   ├── translator/      # Intelligent OID translation (fallback + snmptranslate wrapper)
│   ├── formatter/       # Message formatting using Cue templates
│   ├── output/          # Output handlers (STDOUT, Elasticsearch)
│   └── errors/          # Custom error handling and validation
├── templates/           # Cue template definitions
│   ├── default.cue      # Default template
│   └── linkDown.cue     # Specific trap templates
├── mibs/                # Local MIB files
├── config/              # Configuration files
│   ├── test-config.yml  # Full configuration example
│   └── minimal-config.yml # Minimal configuration for testing
├── docs/                # Documentation
│   ├── tasks.md         # Development tasks and progress
│   ├── plan.md          # Project roadmap and architecture
│   └── *.md             # Additional documentation
├── tools/               # Development and testing tools
└── tests/               # Test files
```

> **Note:** The structure follows Go project best practices with clear separation of concerns. The `internal/translator` package provides intelligent OID translation with comprehensive fallback mechanisms, while the zero-configuration design ensures immediate usability.

### Building

```bash
# Build for current platform
go build -o argus .

# Build for multiple platforms
make build-all

# Run tests
go test ./...

# Run with race detection
go test -race ./...
```

### Static Analysis

The project includes comprehensive static analysis tools for code quality, security, and maintainability:

```bash
# Install all static analysis tools
make install-tools

# Run comprehensive analysis
make analysis

# Run individual tools
make lint          # golangci-lint
make security      # gosec + govulncheck
make staticcheck   # advanced static analysis
make ineffassign   # ineffectual assignments
make gocyclo       # cyclomatic complexity
make dupl          # code duplication

# Use the comprehensive script
./scripts/static-analysis.sh
```

**Available Tools:**

- **golangci-lint** - 20+ linters for code quality
- **gosec** - Security vulnerability scanner
- **govulncheck** - Go vulnerability database scanner
- **staticcheck** - Advanced static analysis
- **ineffassign** - Ineffectual assignment detection
- **gocyclo** - Cyclomatic complexity analysis
- **dupl** - Code duplication detection

Reports are generated in the `reports/` directory. See [docs/static-analysis.md](docs/static-analysis.md) for detailed documentation.

---

## Current Status & Recent Improvements

### ✅ **Production Ready Features**

- **Intelligent OID Translation**: Comprehensive two-tier system with 50+ standard IETF MIB OIDs plus snmptranslate wrapper
- **Zero Configuration**: Works immediately with automatic default logfmt output
- **Human-Readable Output**: Transforms `*******.*******.5.3` → `linkDown`, `*******.*******.0` → `sysUpTime.0`
- **Robust Error Handling**: Graceful degradation with multiple fallback mechanisms
- **High Performance**: Intelligent caching, fast fallback system, concurrent processing
- **Structured Logging**: Uses Go's native `slog` package for consistent, structured output

### 🚀 **Key Capabilities**

**OID Translation Examples**:

- Standard Traps: `coldStart`, `warmStart`, `linkDown`, `linkUp`, `authenticationFailure`
- System Group: `sysDescr.0`, `sysUpTime.0`, `sysContact.0`, `sysName.0`, `sysLocation.0`
- Interface Group: `ifIndex`, `ifDescr`, `ifType`, `ifAdminStatus`, `ifOperStatus`
- SNMP Group: `snmpTrapOID.0`, `snmpTrapEnterprise.0`

**Output Formats**:

- **Automatic Logfmt**: `trap_oid=linkDown sysUpTime.0="4h53m52.96s" ifIndex=2`
- **JSON**: Structured JSON output for programmatic processing
- **Template-based**: Custom Cue templates for specific formatting needs

**Zero Configuration Benefits**:

- Start immediately: `go run . --config config/config.yml`
- No external dependencies required for basic functionality
- Automatic fallback when `snmptranslate` is unavailable
- Default output ensures traps are never silently dropped

### 📊 **Performance Characteristics**

- **Fast Fallback**: Instant translation for 50+ standard OIDs
- **Intelligent Caching**: Simple map-based cache with size limits
- **Multiple Strategies**: 4 different snmptranslate approaches for best results
- **Timeout Protection**: Configurable timeouts prevent hanging (default: 5 seconds)
- **Graceful Degradation**: Continues processing even when external tools fail
