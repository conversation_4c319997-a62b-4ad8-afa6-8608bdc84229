package root

import (
	"fmt"
	"os"
	"strings"

	"argus/cmd/validate"
	"argus/internal/config"
	"argus/internal/errors"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	// Used for flags
	cfgFile      string
	port         int
	mibPaths     string
	outputType   string
	outputFormat string
	debug        bool
	showVersion  bool

	// Version information (to be set during build)
	version = "dev"
	commit  = "none"
	date    = "unknown"
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "argus",
	Short: "Argus SNMP trap receiver",
	Long: `Argus is a high-performance SNMP trap receiver that translates 
cryptic SNMP notifications into human-readable alerts.

It receives SNMP trap messages, translates OIDs to human-readable names 
using a Go wrapper around the snmptranslate command-line tool with local 
MIB files, and outputs structured, readable messages.`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	Run: func(cmd *cobra.Command, args []string) {
		if showVersion {
			fmt.Printf("Argus %s (commit: %s, built at: %s)\n", version, commit, date)
			os.Exit(0)
		}

		// The main application logic will be called here
		RunApp()
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(initConfig)

	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "config/config.yml", "Path to configuration file")

	// Cobra also supports local flags, which will only run
	// when this action is called directly.
	rootCmd.Flags().IntVar(&port, "port", 0, "UDP port to listen for SNMP traps (overrides config file)")
	rootCmd.Flags().StringVar(&mibPaths, "mib-paths", "", "Comma-separated list of directories containing MIB files (overrides config file)")
	rootCmd.Flags().StringVar(&outputType, "output", "", "Output type: stdout, elasticsearch, both (overrides config file)")
	rootCmd.Flags().StringVar(&outputFormat, "format", "", "Output format: text, json, logfmt (overrides config file)")
	rootCmd.Flags().BoolVar(&debug, "debug", false, "Enable debug logging")
	rootCmd.Flags().BoolVar(&showVersion, "version", false, "Show version information")

	// Add subcommands
	rootCmd.AddCommand(validate.ValidateCmd)
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Find home directory.
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)

		// Search config in home directory with name ".argus" (without extension).
		viper.AddConfigPath(home)
		viper.AddConfigPath(".")
		viper.SetConfigType("yaml")
		viper.SetConfigName(".argus")
	}

	viper.SetEnvPrefix("ARGUS")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv() // read in environment variables that match

	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err == nil {
		fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
	}
}

// GetConfig returns the loaded configuration
func GetConfig() (*config.Config, error) {
	cfg, err := config.LoadConfig(cfgFile)
	if err != nil {
		return nil, err
	}

	// Override configuration with command-line flags
	if err := config.UpdateConfigFromFlags(cfg, port, mibPaths, outputType, outputFormat); err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to update configuration from flags")
	}

	return cfg, nil
}

// IsDebug returns whether debug mode is enabled
func IsDebug() bool {
	return debug
}

// GetVersion returns the version information
func GetVersion() (string, string, string) {
	return version, commit, date
}
