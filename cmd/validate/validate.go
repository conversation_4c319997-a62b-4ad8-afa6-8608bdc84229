package validate

import (
	"fmt"
	"os"

	"argus/internal/config"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	// Used for flags
	configFile string
)

// validateCmd represents the validate command
var ValidateCmd = &cobra.Command{
	Use:   "validate",
	Short: "Validate the configuration file",
	Long: `Validate the configuration file for the Argus SNMP trap receiver.

This command checks the configuration file for errors without starting the application.
It verifies that all required fields are present, values are within expected ranges,
and referenced files and directories exist.`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("Validating configuration file:", configFile)

		// Load and validate the configuration
		_, err := config.LoadConfig(configFile)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Configuration validation failed: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("Configuration is valid!")
	},
}

func init() {
	// Get the config file path from viper if it's already set
	if viper.IsSet("config") {
		configFile = viper.GetString("config")
	} else {
		// Default to the same default as the root command
		configFile = "config/config.yml"
	}

	// Add local flags
	ValidateCmd.Flags().StringVar(&configFile, "config", configFile, "Path to configuration file to validate")
}
