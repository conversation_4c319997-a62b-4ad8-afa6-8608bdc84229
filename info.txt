You are a technical writer and Go developer specializing in DevOps tooling. Update and correct the following project files for a Go-based SNMP trap receiver named Argus:

README.md

docs/tasks.md
docs/plan.md
.guidelines.md

Goal: Ensure the documentation clearly reflects that OID translation is handled via a Go wrapper around the snmptranslate command-line tool, using local MIB files.

Context
The snmptranslate binary is guaranteed to be available on the system.

MIB files are located in configurable directories via the mib_paths setting.

The Go wrapper is implemented in the internal/translator package using exec.Command("snmptranslate", ...).

Instructions
Update any mention of custom or internal OID translation logic to reflect the use of the external snmptranslate wrapper.

Revise the architecture diagrams, configuration sections, and implementation plans to reflect the external command usage.

Add a clear prerequisite that snmptranslate must be installed and accessible.

In the MIB Management or OID Translation sections, explain the wrapper's behavior, error cases (e.g., missing MIBs), and how to configure mib_paths.

Optionally include a minimal Go code snippet demonstrating the wrapper usage.

Ensure consistency across all documentation files (README, guidelines, tasks, plan).

Do not change business logic or project architecture. Do not introduce external dependencies. Only remove obsolete references if they are clearly outdated.
