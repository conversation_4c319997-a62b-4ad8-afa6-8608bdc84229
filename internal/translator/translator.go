package translator

import (
	"context"
	"log/slog"
	"os/exec"
	"strings"
	"sync"
	"time"

	"argus/internal/circuitbreaker"
	"argus/internal/errors"
)

// OIDTranslator provides OID to name translation using snmptranslate
type OIDTranslator struct {
	mibPaths       []string
	timeout        time.Duration
	cache          map[string]string
	cacheMu        sync.RWMutex
	maxCache       int
	circuitBreaker *circuitbreaker.CircuitBreaker
}

// Translator is the interface for OID translation
type Translator interface {
	TranslateOID(ctx context.Context, oid string) (string, error)
	Close() error
}

// NewOIDTranslator creates a new OID translator
func NewOIDTranslator(mibPaths []string, timeout time.Duration) (*OIDTranslator, error) {
	// Validate input
	if len(mibPaths) == 0 {
		return nil, errors.New(errors.ValidationError, "mibPaths cannot be empty")
	}
	if timeout <= 0 {
		timeout = 5 * time.Second // Default timeout
	}

	// Check if snmptranslate is available
	if err := checkSnmpTranslateAvailable(); err != nil {
		return nil, errors.Wrap(err, errors.InternalError, "snmptranslate not available")
	}

	// Create circuit breaker for snmptranslate command
	cb, err := circuitbreaker.NewCircuitBreaker(circuitbreaker.Options{
		Name:                "snmptranslate",
		FailureThreshold:    5,
		SuccessThreshold:    2,
		ResetTimeout:        30 * time.Second,
		HalfOpenMaxRequests: 1,
	})
	if err != nil {
		return nil, errors.Wrap(err, errors.InternalError, "Failed to create circuit breaker for snmptranslate")
	}

	return &OIDTranslator{
		mibPaths:       mibPaths,
		timeout:        timeout,
		cache:          make(map[string]string),
		maxCache:       1000, // Limit cache size
		circuitBreaker: cb,
	}, nil
}

// TranslateOID translates an OID to a human-readable name
func (t *OIDTranslator) TranslateOID(ctx context.Context, oid string) (string, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(oid, "oid"); err != nil {
		return "", err
	}

	// Clean OID (remove leading dot if present)
	cleanOID := strings.TrimPrefix(oid, ".")

	// Check cache first
	if cached := t.getCached(cleanOID); cached != "" {
		slog.Debug("OID translation found in cache",
			"oid", oid,
			"name", cached)
		return cached, nil
	}

	// First try fallback for known OIDs (faster and more reliable)
	if fallback := t.getFallbackTranslation(cleanOID); fallback != "" {
		t.setCached(cleanOID, fallback)
		slog.Debug("OID translated using fallback",
			"oid", oid,
			"name", fallback)
		return fallback, nil
	}

	// Try to translate using snmptranslate
	name, err := t.translateWithSnmpTranslate(ctx, cleanOID)
	if err != nil {
		slog.Debug("Failed to translate OID with snmptranslate, returning original",
			"error", err,
			"oid", oid)

		// Return original OID if snmptranslate fails
		return oid, nil
	}

	// Cache the result
	t.setCached(cleanOID, name)

	slog.Debug("OID translated successfully with snmptranslate",
		"oid", oid,
		"name", name)

	return name, nil
}

// translateWithSnmpTranslate calls the snmptranslate command with multiple strategies
func (t *OIDTranslator) translateWithSnmpTranslate(ctx context.Context, oid string) (string, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, t.timeout)
	defer cancel()

	// Try multiple translation strategies
	strategies := []struct {
		name string
		args []string
	}{
		{
			name: "default_no_mib_path",
			args: []string{oid}, // Use default MIB paths
		},
		{
			name: "symbolic_no_mib_path",
			args: []string{"-OS", oid}, // Symbolic output with default paths
		},
		{
			name: "with_mib_paths",
			args: []string{
				"-m", "ALL",
				"-M", strings.Join(t.mibPaths, ":"),
				oid,
			},
		},
		{
			name: "symbolic_with_mib_paths",
			args: []string{
				"-m", "ALL",
				"-M", strings.Join(t.mibPaths, ":"),
				"-OS",
				oid,
			},
		},
	}

	var lastErr error
	for _, strategy := range strategies {
		// Execute snmptranslate command
		cmd := exec.CommandContext(ctx, "snmptranslate", strategy.args...)
		output, err := cmd.Output()
		if err != nil {
			lastErr = err
			continue
		}

		// Parse output
		result := strings.TrimSpace(string(output))
		if result == "" {
			continue
		}

		// Check if we got a meaningful translation
		if isValidTranslation(oid, result) {
			slog.Debug("OID translation successful with strategy",
				"strategy", strategy.name,
				"oid", oid,
				"result", result)
			return result, nil
		}
	}

	// If all strategies failed, return the last error
	if lastErr != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return "", errors.New(errors.InternalError, "snmptranslate command timed out")
		}
		return "", errors.Wrap(lastErr, errors.InternalError, "snmptranslate command failed")
	}

	return "", errors.New(errors.ParseError, "OID not found in MIBs")
}

// isValidTranslation checks if the translation result is meaningful
func isValidTranslation(inputOID, result string) bool {
	// Clean both input and result
	cleanInput := strings.TrimPrefix(inputOID, ".")
	cleanResult := strings.TrimPrefix(result, ".")

	// If result is the same as input, it's not a meaningful translation
	if cleanResult == cleanInput {
		return false
	}

	// If result starts with "iso." followed by numbers, it's not very helpful
	if strings.HasPrefix(cleanResult, "iso.") && isNumericOID(strings.TrimPrefix(cleanResult, "iso.")) {
		return false
	}

	// If result contains meaningful names (not just numbers and dots), it's good
	return containsMeaningfulNames(result)
}

// isNumericOID checks if a string is a numeric OID (only numbers and dots)
func isNumericOID(s string) bool {
	for _, char := range s {
		if char != '.' && (char < '0' || char > '9') {
			return false
		}
	}
	return true
}

// containsMeaningfulNames checks if the result contains alphabetic characters (meaningful names)
func containsMeaningfulNames(s string) bool {
	for _, char := range s {
		if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') {
			return true
		}
	}
	return false
}

// getFallbackTranslation provides comprehensive fallback translations for common OIDs
func (t *OIDTranslator) getFallbackTranslation(oid string) string {
	// Comprehensive known OIDs for fallback
	knownOIDs := map[string]string{
		// System group (*******.2.1.1)
		"*******.2.1.1.1.0": "sysDescr.0",
		"*******.2.1.1.2.0": "sysObjectID.0",
		"*******.2.1.1.3.0": "sysUpTime.0",
		"*******.2.1.1.4.0": "sysContact.0",
		"*******.2.1.1.5.0": "sysName.0",
		"*******.2.1.1.6.0": "sysLocation.0",
		"*******.*******.0": "sysServices.0",

		// SNMP group (*******.2.1.11)
		"*******.********.0": "snmpInPkts.0",
		"*******.********.0": "snmpOutPkts.0",
		"*******.********.0": "snmpInBadVersions.0",

		// Interface group (*******.2.1.2)
		"*******.*******.0":    "ifNumber.0",
		"*******.*******.1.1":  "ifIndex",
		"*******.*******.1.2":  "ifDescr",
		"*******.*******.1.3":  "ifType",
		"*******.*******.1.4":  "ifMtu",
		"*******.*******.1.5":  "ifSpeed",
		"*******.*******.1.6":  "ifPhysAddress",
		"*******.*******.1.7":  "ifAdminStatus",
		"*******.*******.1.8":  "ifOperStatus",
		"*******.*******.1.9":  "ifLastChange",
		"*******.*******.1.10": "ifInOctets",
		"*******.*******.1.11": "ifInUcastPkts",
		"*******.*******.1.12": "ifInNUcastPkts",
		"*******.*******.1.13": "ifInDiscards",
		"*******.*******.1.14": "ifInErrors",
		"*******.*******.1.15": "ifInUnknownProtos",
		"*******.*******.1.16": "ifOutOctets",
		"*******.*******.1.17": "ifOutUcastPkts",
		"*******.*******.1.18": "ifOutNUcastPkts",
		"*******.*******.1.19": "ifOutDiscards",
		"*******.*******.1.20": "ifOutErrors",
		"*******.*******.1.21": "ifOutQLen",
		"*******.*******.1.22": "ifSpecific",

		// SNMP traps (*******.*******)
		"*******.*******.4.1.0": "snmpTrapOID.0",
		"*******.*******.4.3.0": "snmpTrapEnterprise.0",

		// Standard traps (*******.*******.5)
		"*******.*******.5.1": "coldStart",
		"*******.*******.5.2": "warmStart",
		"*******.*******.5.3": "linkDown",
		"*******.*******.5.4": "linkUp",
		"*******.*******.5.5": "authenticationFailure",
		"*******.*******.5.6": "egpNeighborLoss",

		// IP group (*******.2.1.4)
		"*******.*******.0": "ipForwarding.0",
		"*******.*******.0": "ipDefaultTTL.0",
		"*******.*******.0": "ipInReceives.0",
		"*******.*******.0": "ipInHdrErrors.0",

		// TCP group (*******.2.1.6)
		"*******.*******.0": "tcpRtoAlgorithm.0",
		"*******.*******.0": "tcpRtoMin.0",
		"*******.*******.0": "tcpRtoMax.0",

		// UDP group (*******.2.1.7)
		"*******.*******.0": "udpInDatagrams.0",
		"*******.*******.0": "udpNoPorts.0",
		"*******.*******.0": "udpInErrors.0",
		"*******.2.1.7.4.0": "udpOutDatagrams.0",
	}

	// Check for exact match
	if name, exists := knownOIDs[oid]; exists {
		return name
	}

	// Check for prefix match
	for prefix, name := range knownOIDs {
		if strings.HasPrefix(oid, prefix) {
			suffix := oid[len(prefix):]
			if strings.HasPrefix(suffix, ".") {
				return name + suffix
			} else if suffix != "" {
				return name + "." + suffix
			}
		}
	}

	return ""
}

// getCached retrieves a cached translation
func (t *OIDTranslator) getCached(oid string) string {
	t.cacheMu.RLock()
	defer t.cacheMu.RUnlock()
	return t.cache[oid]
}

// setCached stores a translation in cache
func (t *OIDTranslator) setCached(oid, name string) {
	t.cacheMu.Lock()
	defer t.cacheMu.Unlock()

	// Simple cache eviction: clear cache if it gets too large
	if len(t.cache) >= t.maxCache {
		// Clear half the cache (simple strategy)
		newCache := make(map[string]string)
		count := 0
		for k, v := range t.cache {
			if count < t.maxCache/2 {
				newCache[k] = v
				count++
			}
		}
		t.cache = newCache
		slog.Debug("OID translation cache evicted",
			"cache_size", len(t.cache))
	}

	t.cache[oid] = name
}

// Close cleans up the translator
func (t *OIDTranslator) Close() error {
	t.cacheMu.Lock()
	defer t.cacheMu.Unlock()

	// Clear cache
	t.cache = nil

	slog.Debug("OID translator closed")
	return nil
}

// checkSnmpTranslateAvailable checks if snmptranslate command is available
func checkSnmpTranslateAvailable() error {
	cmd := exec.Command("snmptranslate", "-V")
	if err := cmd.Run(); err != nil {
		return errors.Wrap(err, errors.InternalError, "snmptranslate command not found or not executable")
	}
	return nil
}
