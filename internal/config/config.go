package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"argus/internal/errors"

	"log/slog"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server    ServerConfig   `mapstructure:"server"`
	SNMP      SNMPConfig     `mapstructure:"snmp"`
	Output    OutputConfig   `mapstructure:"output"`
	Templates TemplateConfig `mapstructure:"templates"`
	Logging   LoggingConfig  `mapstructure:"logging"`
	Health    HealthConfig   `mapstructure:"health"`
	Filters   []FilterConfig `mapstructure:"filters"`
	Routes    []RouteConfig  `mapstructure:"routes"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
	Port        int    `mapstructure:"port"`
	BindAddress string `mapstructure:"bind_address"`
}

// SNMPConfig contains SNMP-related configuration
type SNMPConfig struct {
	Community string   `mapstructure:"community"`
	Version   string   `mapstructure:"version"`
	MIBPaths  []string `mapstructure:"mib_paths"`
}

// OutputConfig contains output-related configuration
type OutputConfig struct {
	Stdout        StdoutConfig        `mapstructure:"stdout"`
	Elasticsearch ElasticsearchConfig `mapstructure:"elasticsearch"`
}

// StdoutConfig contains stdout output configuration
type StdoutConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Format  string `mapstructure:"format"`
}

// ElasticsearchConfig contains Elasticsearch output configuration
type ElasticsearchConfig struct {
	Enabled      bool                   `mapstructure:"enabled"`
	URL          string                 `mapstructure:"url"`
	Index        string                 `mapstructure:"index"`
	Username     string                 `mapstructure:"username"`
	Password     string                 `mapstructure:"password"`
	APIKey       string                 `mapstructure:"api_key"`
	CustomFields map[string]interface{} `mapstructure:"custom_fields"`
}

// TemplateConfig contains template-related configuration
type TemplateConfig struct {
	Path    string `mapstructure:"path"`
	Default string `mapstructure:"default"`
}

// LoggingConfig contains logging-related configuration
type LoggingConfig struct {
	Level string `mapstructure:"level"`
	File  string `mapstructure:"file"`
}

// HealthConfig contains health check-related configuration
type HealthConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	Port        int    `mapstructure:"port"`
	BindAddress string `mapstructure:"bind_address"`
}

// FilterConfig contains filter configuration
type FilterConfig struct {
	Name        string            `mapstructure:"name"`
	Description string            `mapstructure:"description"`
	Enabled     bool              `mapstructure:"enabled"`
	Conditions  []FilterCondition `mapstructure:"conditions"`
}

// FilterCondition represents a single condition in a filter
type FilterCondition struct {
	Field    string      `mapstructure:"field"`
	Operator string      `mapstructure:"operator"`
	Value    interface{} `mapstructure:"value"`
}

// RouteConfig contains route configuration
type RouteConfig struct {
	Name        string   `mapstructure:"name"`
	Description string   `mapstructure:"description"`
	Enabled     bool     `mapstructure:"enabled"`
	Filters     []string `mapstructure:"filters"`
	Outputs     []string `mapstructure:"outputs"`
}

// LoadConfig loads the configuration from file, environment variables, and command-line flags
func LoadConfig(configFile string) (*Config, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(configFile, "configFile"); err != nil {
		return nil, err
	}

	// Set default configuration values
	setDefaults()

	// Load configuration from file
	if err := loadConfigFile(configFile); err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to load configuration file")
	}

	// Load configuration from environment variables
	loadEnvVars()

	// Parse configuration into struct
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to unmarshal configuration")
	}

	// Validate configuration
	if err := validateConfig(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 162)
	viper.SetDefault("server.bind_address", "0.0.0.0")

	// SNMP defaults
	viper.SetDefault("snmp.community", "public")
	viper.SetDefault("snmp.version", "2c")
	viper.SetDefault("snmp.mib_paths", []string{"/usr/share/snmp/mibs"})

	// Output defaults
	viper.SetDefault("output.stdout.enabled", true)
	viper.SetDefault("output.stdout.format", "text")
	viper.SetDefault("output.elasticsearch.enabled", false)
	viper.SetDefault("output.elasticsearch.url", "http://localhost:9200")
	viper.SetDefault("output.elasticsearch.index", "argus-traps")

	// Template defaults
	viper.SetDefault("templates.path", "./templates")
	viper.SetDefault("templates.default", "default.cue")

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file", "")

	// Health check defaults
	viper.SetDefault("health.enabled", true)
	viper.SetDefault("health.port", 8080)
	viper.SetDefault("health.bind_address", "0.0.0.0")

	// Filter and route defaults
	viper.SetDefault("filters", []FilterConfig{})
	viper.SetDefault("routes", []RouteConfig{})
}

// loadConfigFile loads configuration from a file
func loadConfigFile(configFile string) error {
	viper.SetConfigFile(configFile)
	return viper.ReadInConfig()
}

// loadEnvVars loads configuration from environment variables
func loadEnvVars() {
	viper.SetEnvPrefix("ARGUS")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()
}

// validateConfig validates the configuration
func validateConfig(config *Config) error {
	// Validate server configuration
	if err := errors.ValidatePositive(config.Server.Port, "server.port"); err != nil {
		return err
	}
	if err := errors.ValidateNotEmpty(config.Server.BindAddress, "server.bind_address"); err != nil {
		return err
	}

	// Validate SNMP configuration
	if err := errors.ValidateNotEmpty(config.SNMP.Community, "snmp.community"); err != nil {
		return err
	}
	if err := errors.ValidateOneOf(config.SNMP.Version, []string{"1", "2c", "3"}, "snmp.version"); err != nil {
		return err
	}
	if len(config.SNMP.MIBPaths) == 0 {
		return errors.New(errors.ConfigError, "snmp.mib_paths cannot be empty")
	}

	// Validate MIB paths existence
	for _, path := range config.SNMP.MIBPaths {
		if _, err := os.Stat(path); os.IsNotExist(err) {
			slog.Warn("MIB path does not exist", "path", path)
		}
	}

	// Validate output configuration
	// Note: If no outputs are enabled, a default logfmt output will be created automatically
	if config.Output.Stdout.Enabled {
		if err := errors.ValidateOneOf(config.Output.Stdout.Format, []string{"text", "json", "logfmt"}, "output.stdout.format"); err != nil {
			return err
		}
	}
	if config.Output.Elasticsearch.Enabled {
		if err := errors.ValidateNotEmpty(config.Output.Elasticsearch.URL, "output.elasticsearch.url"); err != nil {
			return err
		}
		if err := errors.ValidateNotEmpty(config.Output.Elasticsearch.Index, "output.elasticsearch.index"); err != nil {
			return err
		}
		// Validate username and password if provided
		if config.Output.Elasticsearch.Username != "" && config.Output.Elasticsearch.Password == "" {
			return errors.New(errors.ConfigError, "Elasticsearch password must be provided when username is set")
		}
	}

	// Validate template configuration
	if err := errors.ValidateNotEmpty(config.Templates.Path, "templates.path"); err != nil {
		return err
	}
	// Check if template path exists
	if _, err := os.Stat(config.Templates.Path); os.IsNotExist(err) {
		return errors.Wrap(err, errors.ConfigError, "Template path does not exist").
			WithMetadata("path", config.Templates.Path)
	}

	if err := errors.ValidateNotEmpty(config.Templates.Default, "templates.default"); err != nil {
		return err
	}

	// Check if default template exists
	defaultTemplatePath := filepath.Join(config.Templates.Path, config.Templates.Default)
	if !strings.HasSuffix(defaultTemplatePath, ".cue") {
		defaultTemplatePath += ".cue"
	}
	if _, err := os.Stat(defaultTemplatePath); os.IsNotExist(err) {
		return errors.Wrap(err, errors.ConfigError, "Default template does not exist").
			WithMetadata("path", defaultTemplatePath)
	}

	// Validate logging configuration
	if config.Logging.Level != "" {
		// Validate log level using slog levels
		validLevels := []string{"debug", "info", "warn", "error"}
		isValid := false
		for _, level := range validLevels {
			if config.Logging.Level == level {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New(errors.ConfigError, "Invalid log level").
				WithMetadata("level", config.Logging.Level).
				WithMetadata("valid_levels", validLevels)
		}
	}

	if config.Logging.File != "" {
		// Check if the directory exists
		dir := filepath.Dir(config.Logging.File)
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			return errors.Wrap(err, errors.ConfigError, "Log file directory does not exist").
				WithMetadata("directory", dir)
		}
	}

	// Validate health check configuration
	if config.Health.Enabled {
		if err := errors.ValidatePositive(config.Health.Port, "health.port"); err != nil {
			return err
		}
		if err := errors.ValidateNotEmpty(config.Health.BindAddress, "health.bind_address"); err != nil {
			return err
		}
	}

	// Validate filter configurations
	filterNames := make(map[string]bool)
	for i, filter := range config.Filters {
		// Validate filter name
		if err := errors.ValidateNotEmpty(filter.Name, fmt.Sprintf("filters[%d].name", i)); err != nil {
			return err
		}

		// Check for duplicate filter names
		if _, exists := filterNames[filter.Name]; exists {
			return errors.New(errors.ConfigError, fmt.Sprintf("Duplicate filter name: %s", filter.Name))
		}
		filterNames[filter.Name] = true

		// Validate filter conditions
		if len(filter.Conditions) == 0 {
			return errors.New(errors.ConfigError, fmt.Sprintf("Filter %s has no conditions", filter.Name))
		}

		// Validate each condition
		for j, condition := range filter.Conditions {
			// Validate field
			if err := errors.ValidateNotEmpty(condition.Field, fmt.Sprintf("filters[%d].conditions[%d].field", i, j)); err != nil {
				return err
			}

			// Validate operator
			validOperators := []string{"equals", "not_equals", "contains", "not_contains", "starts_with", "ends_with", "matches", "exists", "not_exists"}
			if err := errors.ValidateOneOf(condition.Operator, validOperators, fmt.Sprintf("filters[%d].conditions[%d].operator", i, j)); err != nil {
				return err
			}

			// Validate value (only required for operators other than exists/not_exists)
			if condition.Operator != "exists" && condition.Operator != "not_exists" {
				if condition.Value == nil {
					return errors.New(errors.ConfigError, fmt.Sprintf("Value is required for operator %s in filter %s", condition.Operator, filter.Name))
				}
			}
		}
	}

	// Validate route configurations
	routeNames := make(map[string]bool)
	for i, route := range config.Routes {
		// Validate route name
		if err := errors.ValidateNotEmpty(route.Name, fmt.Sprintf("routes[%d].name", i)); err != nil {
			return err
		}

		// Check for duplicate route names
		if _, exists := routeNames[route.Name]; exists {
			return errors.New(errors.ConfigError, fmt.Sprintf("Duplicate route name: %s", route.Name))
		}
		routeNames[route.Name] = true

		// Validate filters
		if len(route.Filters) == 0 {
			return errors.New(errors.ConfigError, fmt.Sprintf("Route %s has no filters", route.Name))
		}

		// Validate filter references
		for _, filterName := range route.Filters {
			if _, exists := filterNames[filterName]; !exists {
				return errors.New(errors.ConfigError, fmt.Sprintf("Route %s references undefined filter: %s", route.Name, filterName))
			}
		}

		// Validate outputs
		if len(route.Outputs) == 0 {
			return errors.New(errors.ConfigError, fmt.Sprintf("Route %s has no outputs", route.Name))
		}

		// Validate output references (we can't fully validate this here as outputs are registered dynamically)
		// Just check that they're not empty
		for j, outputName := range route.Outputs {
			if err := errors.ValidateNotEmpty(outputName, fmt.Sprintf("routes[%d].outputs[%d]", i, j)); err != nil {
				return err
			}
		}
	}

	return nil
}

// UpdateConfigFromFlags updates the configuration with command-line flags
func UpdateConfigFromFlags(config *Config, port int, mibPaths string, outputType string, outputFormat string) error {
	// Validate input
	if err := errors.ValidateNotNil(config, "config"); err != nil {
		return err
	}

	if port != 0 {
		if err := errors.ValidatePositive(port, "port"); err != nil {
			return err
		}
		config.Server.Port = port
	}

	if mibPaths != "" {
		paths := strings.Split(mibPaths, ",")
		for i, path := range paths {
			if err := errors.ValidateNotEmpty(path, fmt.Sprintf("mibPaths[%d]", i)); err != nil {
				return err
			}
		}
		config.SNMP.MIBPaths = paths
	}

	if outputType != "" {
		if err := errors.ValidateOneOf(outputType, []string{"stdout", "elasticsearch", "both"}, "outputType"); err != nil {
			return err
		}

		switch outputType {
		case "stdout":
			config.Output.Stdout.Enabled = true
			config.Output.Elasticsearch.Enabled = false
		case "elasticsearch":
			config.Output.Stdout.Enabled = false
			config.Output.Elasticsearch.Enabled = true
		case "both":
			config.Output.Stdout.Enabled = true
			config.Output.Elasticsearch.Enabled = true
		}
	}

	if outputFormat != "" {
		if err := errors.ValidateOneOf(outputFormat, []string{"text", "json", "logfmt"}, "outputFormat"); err != nil {
			return err
		}
		config.Output.Stdout.Format = outputFormat
	}

	return nil
}

// ConfigChangeCallback is a function that is called when the configuration changes
type ConfigChangeCallback func(*Config) error

// ConfigWatcher watches for configuration file changes and reloads the configuration
type ConfigWatcher struct {
	configFile string
	callbacks  []ConfigChangeCallback
	mu         sync.Mutex
	debounce   time.Duration
	lastEvent  time.Time
}

// NewConfigWatcher creates a new ConfigWatcher
func NewConfigWatcher(configFile string) (*ConfigWatcher, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(configFile, "configFile"); err != nil {
		return nil, err
	}

	return &ConfigWatcher{
		configFile: configFile,
		callbacks:  make([]ConfigChangeCallback, 0),
		debounce:   500 * time.Millisecond, // Default debounce time
	}, nil
}

// RegisterCallback registers a callback function to be called when the configuration changes
func (w *ConfigWatcher) RegisterCallback(callback ConfigChangeCallback) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.callbacks = append(w.callbacks, callback)
}

// SetDebounceTime sets the debounce time for configuration change events
func (w *ConfigWatcher) SetDebounceTime(duration time.Duration) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.debounce = duration
}

// Start starts watching for configuration file changes
func (w *ConfigWatcher) Start() error {
	// Initialize fsnotify watcher
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return errors.Wrap(err, errors.ConfigError, "Failed to create file watcher")
	}

	// Watch the configuration file
	if err := watcher.Add(filepath.Dir(w.configFile)); err != nil {
		watcher.Close()
		return errors.Wrap(err, errors.ConfigError, "Failed to watch configuration file").
			WithMetadata("path", w.configFile)
	}

	// Start watching for events in a goroutine
	go func() {
		defer watcher.Close()

		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}

				// Check if the event is for our config file
				if filepath.Base(event.Name) != filepath.Base(w.configFile) {
					continue
				}

				// Check if the event is a write or create event
				if event.Op&(fsnotify.Write|fsnotify.Create) == 0 {
					continue
				}

				// Debounce events
				w.mu.Lock()
				now := time.Now()
				if now.Sub(w.lastEvent) < w.debounce {
					w.mu.Unlock()
					continue
				}
				w.lastEvent = now
				w.mu.Unlock()

				// Reload configuration
				slog.Info("Configuration file changed, reloading...", "path", w.configFile)
				if err := w.reloadConfig(); err != nil {
					slog.Error("Failed to reload configuration", "error", err)
				}

			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				slog.Error("Error watching configuration file", "error", err)
			}
		}
	}()

	slog.Info("Started watching configuration file for changes", "path", w.configFile)
	return nil
}

// reloadConfig reloads the configuration and calls all registered callbacks
func (w *ConfigWatcher) reloadConfig() error {
	// Load the new configuration
	newConfig, err := LoadConfig(w.configFile)
	if err != nil {
		return errors.Wrap(err, errors.ConfigError, "Failed to reload configuration")
	}

	// Call all registered callbacks with the new configuration
	w.mu.Lock()
	callbacks := make([]ConfigChangeCallback, len(w.callbacks))
	copy(callbacks, w.callbacks)
	w.mu.Unlock()

	for _, callback := range callbacks {
		if err := callback(newConfig); err != nil {
			slog.Error("Error in configuration change callback", "error", err)
			// Continue with other callbacks even if one fails
		}
	}

	slog.Info("Configuration reloaded successfully")
	return nil
}
