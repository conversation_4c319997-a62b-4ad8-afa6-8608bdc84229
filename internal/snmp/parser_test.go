package snmp

import (
	"context"
	"net"
	"testing"

	"github.com/gosnmp/gosnmp"
)

func TestGetOIDName(t *testing.T) {
	parser := NewTrapParser()

	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "empty string",
			input:       "",
			expected:    "",
			expectError: true,
		},
		{
			name:        "known OID with leading dot",
			input:       ".*******.*******.0",
			expected:    "sysUpTime.0",
			expectError: false,
		},
		{
			name:        "known OID without leading dot",
			input:       "*******.*******.0",
			expected:    "sysUpTime.0",
			expectError: false,
		},
		{
			name:        "known OID prefix with additional suffix",
			input:       "*******.2.1.2.2.1.1.5",
			expected:    "ifIndex.5",
			expectError: false,
		},
		{
			name:        "unknown OID",
			input:       "1.2.3.4.5",
			expected:    "1.2.3.4.5",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.getOIDName(context.Background(), tt.input)

			// Check error expectation
			if tt.expectError && err == nil {
				t.Errorf("getOIDName(%q) expected error but got none", tt.input)
			}
			if !tt.expectError && err != nil {
				t.Errorf("getOIDName(%q) unexpected error: %v", tt.input, err)
			}

			// If we don't expect an error, check the result
			if !tt.expectError {
				if result != tt.expected {
					t.Errorf("getOIDName(%q) = %q, want %q", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestGetVariableType(t *testing.T) {
	tests := []struct {
		name     string
		varType  gosnmp.Asn1BER
		expected string
	}{
		{
			name:     "Integer",
			varType:  gosnmp.Integer,
			expected: "Integer",
		},
		{
			name:     "OctetString",
			varType:  gosnmp.OctetString,
			expected: "OctetString",
		},
		{
			name:     "ObjectIdentifier",
			varType:  gosnmp.ObjectIdentifier,
			expected: "ObjectIdentifier",
		},
		{
			name:     "IPAddress",
			varType:  gosnmp.IPAddress,
			expected: "IPAddress",
		},
		{
			name:     "Counter32",
			varType:  gosnmp.Counter32,
			expected: "Counter32",
		},
		{
			name:     "Gauge32",
			varType:  gosnmp.Gauge32,
			expected: "Gauge32",
		},
		{
			name:     "TimeTicks",
			varType:  gosnmp.TimeTicks,
			expected: "TimeTicks",
		},
		{
			name:     "Counter64",
			varType:  gosnmp.Counter64,
			expected: "Counter64",
		},
		{
			name:     "Null",
			varType:  gosnmp.Null,
			expected: "Null",
		},
		{
			name:     "NoSuchObject",
			varType:  gosnmp.NoSuchObject,
			expected: "NoSuchObject",
		},
		{
			name:     "NoSuchInstance",
			varType:  gosnmp.NoSuchInstance,
			expected: "NoSuchInstance",
		},
		{
			name:     "EndOfMibView",
			varType:  gosnmp.EndOfMibView,
			expected: "EndOfMibView",
		},
		{
			name:     "Unknown",
			varType:  gosnmp.Asn1BER(99),
			expected: "Unknown (99)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getVariableType(tt.varType)
			if result != tt.expected {
				t.Errorf("getVariableType(%v) = %q, want %q", tt.varType, result, tt.expected)
			}
		})
	}
}

func TestFormatVariableValue(t *testing.T) {
	tests := []struct {
		name     string
		varType  gosnmp.Asn1BER
		value    interface{}
		expected interface{}
	}{
		{
			name:     "TimeTicks",
			varType:  gosnmp.TimeTicks,
			value:    uint32(123456),
			expected: "20m34.56s (123456)",
		},
		{
			name:     "OctetString",
			varType:  gosnmp.OctetString,
			value:    []byte("test string"),
			expected: "test string",
		},
		{
			name:     "Integer",
			varType:  gosnmp.Integer,
			value:    int(42),
			expected: int(42),
		},
		{
			name:     "TimeTicks with non-uint32 value",
			varType:  gosnmp.TimeTicks,
			value:    "not a uint32",
			expected: "not a uint32",
		},
		{
			name:     "OctetString with non-byte-slice value",
			varType:  gosnmp.OctetString,
			value:    42,
			expected: 42,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatVariableValue(tt.varType, tt.value)

			// For string results, we need to check if they contain the expected value
			// because the exact format might vary (e.g., time duration formatting)
			if resultStr, ok := result.(string); ok {
				if expectedStr, ok := tt.expected.(string); ok {
					if resultStr != expectedStr {
						t.Errorf("formatVariableValue(%v, %v) = %q, want %q", tt.varType, tt.value, resultStr, expectedStr)
					}
					return
				}
			}

			// For non-string results, we can compare directly
			if result != tt.expected {
				t.Errorf("formatVariableValue(%v, %v) = %v, want %v", tt.varType, tt.value, result, tt.expected)
			}
		})
	}
}

func TestFormatOID(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "empty string",
			input:       "",
			expected:    "",
			expectError: true,
		},
		{
			name:        "already formatted",
			input:       "*******.4.1",
			expected:    "*******.4.1",
			expectError: false,
		},
		{
			name:        "needs formatting",
			input:       "13614",
			expected:    "*******.4",
			expectError: false,
		},
		{
			name:        "non-digit characters",
			input:       "1a3614",
			expected:    "",
			expectError: true,
		},
		{
			name:        "single digit",
			input:       "1",
			expected:    "1",
			expectError: false,
		},
		{
			name:        "long OID",
			input:       "1361412345",
			expected:    "*******.4.1.2.3.4.5",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := FormatOID(tt.input)

			// Check error expectation
			if tt.expectError && err == nil {
				t.Errorf("FormatOID(%q) expected error but got none", tt.input)
			}
			if !tt.expectError && err != nil {
				t.Errorf("FormatOID(%q) unexpected error: %v", tt.input, err)
			}

			// If we don't expect an error, check the result
			if !tt.expectError {
				if result != tt.expected {
					t.Errorf("FormatOID(%q) = %q, want %q", tt.input, result, tt.expected)
				}
			}
		})
	}
}

// TestTrapMessageCreation tests the creation of TrapMessage structures
func TestTrapMessageCreation(t *testing.T) {
	tests := []struct {
		name     string
		sourceIP string
		oid      string
		expected bool
	}{
		{
			name:     "valid trap message",
			sourceIP: "***********",
			oid:      "*******.*******.5.1",
			expected: true,
		},
		{
			name:     "empty source IP",
			sourceIP: "",
			oid:      "*******.*******.5.1",
			expected: false,
		},
		{
			name:     "empty OID",
			sourceIP: "***********",
			oid:      "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			trap := &TrapMessage{
				SourceIP:  tt.sourceIP,
				Community: "public",
				Version:   "2c",
				TrapType:  "coldStart",
				OID:       tt.oid,
				Variables: []TrapVariable{},
			}

			// Basic validation
			isValid := trap.SourceIP != "" && trap.OID != ""
			if isValid != tt.expected {
				t.Errorf("TrapMessage validation = %v, want %v", isValid, tt.expected)
			}
		})
	}
}

func TestParseTrapSNMPv1Enterprise(t *testing.T) {
	parser := NewTrapParser()
	addr := &net.UDPAddr{
		IP:   net.ParseIP("*************"),
		Port: 12345,
	}

	// Test SNMPv1 trap with enterprise OID
	packet := &gosnmp.SnmpPacket{
		Version:   gosnmp.Version1,
		Community: "public",
		PDUType:   gosnmp.Trap,
		SnmpTrap: gosnmp.SnmpTrap{
			Enterprise:   "*******.4.1.12345",
			AgentAddress: "*************",
			GenericTrap:  6,
			SpecificTrap: 1,
			Timestamp:    123456,
		},
		Variables: []gosnmp.SnmpPDU{
			{
				Name:  "*******.*******.0",
				Type:  gosnmp.TimeTicks,
				Value: uint32(123456),
			},
		},
	}

	msg, err := parser.ParseTrap(context.Background(), packet, addr)
	if err != nil {
		t.Fatalf("ParseTrap() error: %v", err)
	}

	// Verify SNMPv1 trap parsing
	if msg.TrapType != "SNMPv1Trap" {
		t.Errorf("ParseTrap() trap type = %v, want SNMPv1Trap", msg.TrapType)
	}
	if msg.OID != "*******.4.1.12345" {
		t.Errorf("ParseTrap() OID = %v, want *******.4.1.12345", msg.OID)
	}
	if msg.Version != "SNMPv1" {
		t.Errorf("ParseTrap() version = %v, want SNMPv1", msg.Version)
	}

	// Verify SNMPv1-specific fields in raw data
	if enterprise, ok := msg.RawData["enterprise"]; !ok || enterprise != "*******.4.1.12345" {
		t.Errorf("ParseTrap() enterprise in raw data = %v, want *******.4.1.12345", enterprise)
	}
	if agentAddr, ok := msg.RawData["agentAddress"]; !ok || agentAddr != "*************" {
		t.Errorf("ParseTrap() agentAddress in raw data = %v, want *************", agentAddr)
	}
	if genericTrap, ok := msg.RawData["genericTrap"]; !ok || genericTrap != 6 {
		t.Errorf("ParseTrap() genericTrap in raw data = %v, want 6", genericTrap)
	}
	if specificTrap, ok := msg.RawData["specificTrap"]; !ok || specificTrap != 1 {
		t.Errorf("ParseTrap() specificTrap in raw data = %v, want 1", specificTrap)
	}
}
