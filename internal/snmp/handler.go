package snmp

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"time"

	"argus/internal/config"
	"argus/internal/errors"
	"argus/internal/translator"

	"github.com/gosnmp/gosnmp"
)

// DefaultTrapHandler is the default implementation of TrapHandler
type DefaultTrapHandler struct {
	config       *config.Config
	parser       *TrapParser
	outputs      map[string]TrapOutput
	filterEngine *FilterEngine
}

// TrapOutput is an interface for handling trap output
type TrapOutput interface {
	Output(ctx context.Context, msg *TrapMessage) error
}

// NewDefaultTrapHandler creates a new DefaultTrapHandler
func NewDefaultTrapHandler(cfg *config.Config) (*DefaultTrapHandler, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}

	// Create OID translator
	oidTranslator, err := translator.NewOIDTranslator(cfg.SNMP.MIBPaths, 5*time.Second)
	if err != nil {
		slog.Warn("Failed to create OID translator, using fallback translation",
			"error", err)
		// Continue without translator - parser will use fallback
		return &DefaultTrapHandler{
			config:  cfg,
			parser:  NewTrapParser(),
			outputs: make(map[string]TrapOutput),
		}, nil
	}

	slog.Info("OID translator initialized successfully",
		"mib_paths", cfg.SNMP.MIBPaths)

	// Create filter engine
	filterEngine, err := NewFilterEngine(cfg)
	if err != nil {
		slog.Warn("Failed to create filter engine, continuing without filtering",
			"error", err)
		// Continue without filter engine
		return &DefaultTrapHandler{
			config:  cfg,
			parser:  NewTrapParserWithTranslator(oidTranslator),
			outputs: make(map[string]TrapOutput),
		}, nil
	}

	return &DefaultTrapHandler{
		config:       cfg,
		parser:       NewTrapParserWithTranslator(oidTranslator),
		outputs:      make(map[string]TrapOutput),
		filterEngine: filterEngine,
	}, nil
}

// RegisterOutput registers an output handler with a name
func (h *DefaultTrapHandler) RegisterOutput(name string, output TrapOutput) error {
	// Validate input
	if err := errors.ValidateNotEmpty(name, "name"); err != nil {
		return err
	}
	if err := errors.ValidateNotNil(output, "output"); err != nil {
		return err
	}

	// Check if output with this name already exists
	if _, exists := h.outputs[name]; exists {
		return errors.New(errors.ValidationError, fmt.Sprintf("Output with name '%s' already registered", name))
	}

	h.outputs[name] = output
	slog.Info("Registered output handler", "name", name)
	return nil
}

// HandleTrap handles an SNMP trap
func (h *DefaultTrapHandler) HandleTrap(ctx context.Context, packet *gosnmp.SnmpPacket, addr *net.UDPAddr) error {
	// Validate input
	if err := errors.ValidateNotNil(packet, "packet"); err != nil {
		return err
	}
	if err := errors.ValidateNotNil(addr, "addr"); err != nil {
		return err
	}

	// Parse the trap
	msg, err := h.parser.ParseTrap(ctx, packet, addr)
	if err != nil {
		slog.Error("Failed to parse SNMP trap",
			"error", err,
			"source", addr.String())
		return err
	}

	// Log the trap
	trapOIDForLog := msg.OID
	if trapOIDName, exists := msg.RawData["trapOIDName"]; exists {
		trapOIDForLog = trapOIDName.(string)
	}

	slog.Info("Received SNMP trap",
		"source", msg.SourceIP,
		"community", msg.Community,
		"version", msg.Version,
		"trap_type", msg.TrapType,
		"trap_oid", trapOIDForLog,
		"variables", len(msg.Variables))

	// If no filter engine is configured, send to all registered outputs
	if h.filterEngine == nil {
		slog.Debug("No filter engine configured, sending to all outputs")
		for _, output := range h.outputs {
			if err := output.Output(ctx, msg); err != nil {
				slog.Error("Failed to output SNMP trap",
					"error", err,
					"source", msg.SourceIP)
			}
		}
		return nil
	}

	// Get outputs for this trap based on filters and routes
	outputNames := h.filterEngine.GetOutputsForTrap(msg)

	// If no outputs match, check if we should use default behavior
	if len(outputNames) == 0 {
		// If no routes are configured at all, send to all outputs as default
		if !h.filterEngine.HasRoutes() {
			slog.Debug("No routes configured, sending to all outputs as default")
			for _, output := range h.outputs {
				if err := output.Output(ctx, msg); err != nil {
					slog.Error("Failed to output SNMP trap",
						"error", err,
						"source", msg.SourceIP)
				}
			}
			return nil
		}

		// Routes are configured but none match - log and return
		slog.Debug("No outputs match for trap",
			"source", msg.SourceIP,
			"trap_type", msg.TrapType,
			"oid", msg.OID)
		return nil
	}

	// Send to matching outputs
	for _, outputName := range outputNames {
		output, exists := h.outputs[outputName]
		if !exists {
			slog.Warn("Output not found", "name", outputName)
			continue
		}

		if err := output.Output(ctx, msg); err != nil {
			slog.Error("Failed to output SNMP trap",
				"error", err,
				"source", msg.SourceIP,
				"output", outputName)
		} else {
			slog.Debug("Sent trap to output",
				"output", outputName,
				"source", msg.SourceIP,
				"trap_type", msg.TrapType)
		}
	}

	return nil
}

// ConsoleOutput is a simple output handler that logs to the console
type ConsoleOutput struct {
	format string
}

// NewConsoleOutput creates a new ConsoleOutput
func NewConsoleOutput(format string) (*ConsoleOutput, error) {
	// Validate input
	if err := errors.ValidateOneOf(format, []string{"text", "json", "logfmt"}, "format"); err != nil {
		return nil, err
	}

	return &ConsoleOutput{
		format: format,
	}, nil
}

// Output outputs a trap message to the console
func (o *ConsoleOutput) Output(ctx context.Context, msg *TrapMessage) error {
	// Validate input
	if err := errors.ValidateNotNil(msg, "msg"); err != nil {
		return err
	}

	switch o.format {
	case "json":
		// Log as JSON
		slog.Info("SNMP Trap", "trap", msg)
	case "logfmt":
		// Log as logfmt (key=value pairs)
		attrs := []slog.Attr{
			slog.String("source", msg.SourceIP),
			slog.String("community", msg.Community),
			slog.String("version", msg.Version),
			slog.String("trap_type", msg.TrapType),
		}

		// Add trap OID - use translated name if available, otherwise use numeric OID
		if trapOIDName, exists := msg.RawData["trapOIDName"]; exists {
			attrs = append(attrs, slog.String("trap_oid", trapOIDName.(string)))
		} else {
			attrs = append(attrs, slog.String("trap_oid", msg.OID))
		}

		attrs = append(attrs, slog.Time("timestamp", msg.Timestamp))

		// Add variables
		for _, v := range msg.Variables {
			attrs = append(attrs, slog.Any(v.Name, v.Value))
		}

		slog.LogAttrs(context.TODO(), slog.LevelInfo, "SNMP Trap", attrs...)
	default:
		// Log as structured log (text format)
		attrs := []slog.Attr{
			slog.String("source", msg.SourceIP),
			slog.String("trap_type", msg.TrapType),
			slog.String("oid", msg.OID),
		}

		// Add variables
		for _, v := range msg.Variables {
			attrs = append(attrs, slog.Any(v.Name, v.Value))
		}

		slog.LogAttrs(context.TODO(), slog.LevelInfo, "SNMP Trap", attrs...)
	}

	return nil
}
