package snmp

import (
	"fmt"
	"log/slog"
	"reflect"
	"regexp"
	"strings"

	"argus/internal/config"
	"argus/internal/errors"
)

// FilterEngine handles filtering of SNMP trap messages
type FilterEngine struct {
	filters map[string]*Filter
	routes  map[string]*Route
}

// Filter represents a filter for SNMP trap messages
type Filter struct {
	name        string
	description string
	enabled     bool
	conditions  []FilterCondition
}

// FilterCondition represents a condition in a filter
type FilterCondition struct {
	field    string
	operator string
	value    interface{}
}

// Route represents a route for SNMP trap messages
type Route struct {
	name        string
	description string
	enabled     bool
	filters     []*Filter
	outputs     []string
}

// NewFilterEngine creates a new FilterEngine
func NewFilterEngine(cfg *config.Config) (*FilterEngine, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}

	engine := &FilterEngine{
		filters: make(map[string]*Filter),
		routes:  make(map[string]*Route),
	}

	// Create filters
	for _, filterCfg := range cfg.Filters {
		if !filterCfg.Enabled {
			slog.Debug("Skipping disabled filter", "name", filterCfg.Name)
			continue
		}

		filter := &Filter{
			name:        filterCfg.Name,
			description: filterCfg.Description,
			enabled:     filterCfg.Enabled,
			conditions:  make([]FilterCondition, 0, len(filterCfg.Conditions)),
		}

		// Create conditions
		for _, conditionCfg := range filterCfg.Conditions {
			condition := FilterCondition{
				field:    conditionCfg.Field,
				operator: conditionCfg.Operator,
				value:    conditionCfg.Value,
			}
			filter.conditions = append(filter.conditions, condition)
		}

		engine.filters[filter.name] = filter
		slog.Info("Added filter", "name", filter.name, "conditions", len(filter.conditions))
	}

	// Create routes
	for _, routeCfg := range cfg.Routes {
		if !routeCfg.Enabled {
			slog.Debug("Skipping disabled route", "name", routeCfg.Name)
			continue
		}

		route := &Route{
			name:        routeCfg.Name,
			description: routeCfg.Description,
			enabled:     routeCfg.Enabled,
			filters:     make([]*Filter, 0, len(routeCfg.Filters)),
			outputs:     routeCfg.Outputs,
		}

		// Add filters to route
		for _, filterName := range routeCfg.Filters {
			filter, exists := engine.filters[filterName]
			if !exists {
				slog.Warn("Filter not found for route", "route", route.name, "filter", filterName)
				continue
			}
			route.filters = append(route.filters, filter)
		}

		engine.routes[route.name] = route
		slog.Info("Added route", "name", route.name, "filters", len(route.filters), "outputs", len(route.outputs))
	}

	return engine, nil
}

// HasRoutes returns true if the filter engine has any routes configured
func (e *FilterEngine) HasRoutes() bool {
	return len(e.routes) > 0
}

// GetRoutesForTrap returns the routes that match the given trap message
func (e *FilterEngine) GetRoutesForTrap(msg *TrapMessage) []*Route {
	// If no routes are configured, return empty slice
	if len(e.routes) == 0 {
		return []*Route{}
	}

	var matchedRoutes []*Route

	// Check each route
	for _, route := range e.routes {
		if !route.enabled {
			continue
		}

		// If no filters are configured for this route, it matches all traps
		if len(route.filters) == 0 {
			matchedRoutes = append(matchedRoutes, route)
			continue
		}

		// Check if any filter matches
		for _, filter := range route.filters {
			if e.filterMatches(filter, msg) {
				matchedRoutes = append(matchedRoutes, route)
				break
			}
		}
	}

	return matchedRoutes
}

// filterMatches checks if a filter matches a trap message
func (e *FilterEngine) filterMatches(filter *Filter, msg *TrapMessage) bool {
	if !filter.enabled {
		return false
	}

	// If no conditions are configured, the filter matches all traps
	if len(filter.conditions) == 0 {
		return true
	}

	// Check if all conditions match
	for _, condition := range filter.conditions {
		if !e.conditionMatches(condition, msg) {
			return false
		}
	}

	return true
}

// conditionMatches checks if a condition matches a trap message
func (e *FilterEngine) conditionMatches(condition FilterCondition, msg *TrapMessage) bool {
	// Get the field value
	fieldValue, err := e.getFieldValue(condition.field, msg)
	if err != nil {
		slog.Warn("Error getting field value", "error", err, "field", condition.field)
		return false
	}

	// Handle exists/not_exists operators
	if condition.operator == "exists" {
		return fieldValue != nil
	}
	if condition.operator == "not_exists" {
		return fieldValue == nil
	}

	// If field value is nil, it can't match any other operators
	if fieldValue == nil {
		return false
	}

	// Convert field value to string for string operations
	fieldValueStr := fmt.Sprintf("%v", fieldValue)

	// Check condition based on operator
	switch condition.operator {
	case "equals":
		return reflect.DeepEqual(fieldValue, condition.value)
	case "not_equals":
		return !reflect.DeepEqual(fieldValue, condition.value)
	case "contains":
		conditionValueStr := fmt.Sprintf("%v", condition.value)
		return strings.Contains(fieldValueStr, conditionValueStr)
	case "not_contains":
		conditionValueStr := fmt.Sprintf("%v", condition.value)
		return !strings.Contains(fieldValueStr, conditionValueStr)
	case "starts_with":
		conditionValueStr := fmt.Sprintf("%v", condition.value)
		return strings.HasPrefix(fieldValueStr, conditionValueStr)
	case "ends_with":
		conditionValueStr := fmt.Sprintf("%v", condition.value)
		return strings.HasSuffix(fieldValueStr, conditionValueStr)
	case "matches":
		conditionValueStr := fmt.Sprintf("%v", condition.value)
		re, err := regexp.Compile(conditionValueStr)
		if err != nil {
			slog.Warn("Invalid regex pattern", "error", err, "pattern", conditionValueStr)
			return false
		}
		return re.MatchString(fieldValueStr)
	default:
		slog.Warn("Unknown operator", "operator", condition.operator)
		return false
	}
}

// getFieldValue gets the value of a field from a trap message
func (e *FilterEngine) getFieldValue(field string, msg *TrapMessage) (interface{}, error) {
	// Check for special fields
	switch field {
	case "source_ip":
		return msg.SourceIP, nil
	case "community":
		return msg.Community, nil
	case "version":
		return msg.Version, nil
	case "trap_type":
		return msg.TrapType, nil
	case "oid":
		return msg.OID, nil
	case "timestamp":
		return msg.Timestamp, nil
	}

	// Check for variable fields
	if strings.HasPrefix(field, "var.") {
		varName := strings.TrimPrefix(field, "var.")
		for _, v := range msg.Variables {
			if v.Name == varName {
				return v.Value, nil
			}
		}
		return nil, nil // Variable not found
	}

	// Check for raw data fields
	if strings.HasPrefix(field, "raw.") {
		rawField := strings.TrimPrefix(field, "raw.")
		if value, exists := msg.RawData[rawField]; exists {
			return value, nil
		}
		return nil, nil // Raw field not found
	}

	return nil, errors.New(errors.ValidationError, fmt.Sprintf("Unknown field: %s", field))
}

// GetOutputsForTrap returns the outputs that should receive the given trap message
func (e *FilterEngine) GetOutputsForTrap(msg *TrapMessage) []string {
	var outputs []string
	outputMap := make(map[string]bool) // To avoid duplicates

	// Get routes that match the trap
	routes := e.GetRoutesForTrap(msg)

	// If no routes match, return empty slice
	if len(routes) == 0 {
		return outputs
	}

	// Collect outputs from all matching routes
	for _, route := range routes {
		for _, output := range route.outputs {
			if !outputMap[output] {
				outputs = append(outputs, output)
				outputMap[output] = true
			}
		}
	}

	return outputs
}
