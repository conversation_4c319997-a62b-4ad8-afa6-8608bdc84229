package snmp

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"strings"
	"sync"
	"time"

	"argus/internal/config"
	"argus/internal/errors"

	"github.com/gosnmp/gosnmp"
)

// TrapListener represents an SNMP trap listener
type TrapListener struct {
	config     *config.Config
	conn       *net.UDPConn
	handler    TrapHandler
	wg         sync.WaitGroup
	cancelFunc context.CancelFunc
}

// TrapHandler is an interface for handling SNMP traps
type TrapHandler interface {
	HandleTrap(ctx context.Context, trap *gosnmp.SnmpPacket, addr *net.UDPAddr) error
}

// NewTrapListener creates a new SNMP trap listener
func NewTrapListener(cfg *config.Config, handler TrapHandler) (*TrapListener, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}
	if err := errors.ValidateNotNil(handler, "handler"); err != nil {
		return nil, err
	}

	return &TrapListener{
		config:  cfg,
		handler: handler,
	}, nil
}

// Start starts the SNMP trap listener
func (l *TrapListener) Start(ctx context.Context) error {
	// Validate input
	if err := errors.ValidateNotNil(ctx, "ctx"); err != nil {
		return err
	}

	// Create a cancellable context
	ctx, cancel := context.WithCancel(ctx)
	l.cancelFunc = cancel

	// Set up UDP listener
	addr := fmt.Sprintf("%s:%d", l.config.Server.BindAddress, l.config.Server.Port)
	udpAddr, err := net.ResolveUDPAddr("udp", addr)
	if err != nil {
		return fmt.Errorf("failed to resolve UDP address: %w", err)
	}

	conn, err := net.ListenUDP("udp", udpAddr)
	if err != nil {
		return fmt.Errorf("failed to listen on UDP: %w", err)
	}
	l.conn = conn

	slog.Info("SNMP trap listener started",
		"address", addr,
		"community", l.config.SNMP.Community,
		"version", l.config.SNMP.Version)

	// Start the listener goroutine
	l.wg.Add(1)
	go l.listen(ctx)

	return nil
}

// Stop stops the SNMP trap listener
func (l *TrapListener) Stop() error {
	if l.cancelFunc != nil {
		l.cancelFunc()
	}

	if l.conn != nil {
		if err := l.conn.Close(); err != nil {
			return fmt.Errorf("failed to close UDP connection: %w", err)
		}
	}

	// Wait for the listener goroutine to finish
	l.wg.Wait()
	slog.Info("SNMP trap listener stopped")
	return nil
}

// listen listens for incoming SNMP traps
func (l *TrapListener) listen(ctx context.Context) {
	defer l.wg.Done()

	// Buffer for incoming packets
	buffer := make([]byte, 4096)

	for {
		select {
		case <-ctx.Done():
			return
		default:
			// Set read deadline to allow for context cancellation
			if err := l.conn.SetReadDeadline(time.Now().Add(1 * time.Second)); err != nil {
				slog.Error("Failed to set read deadline", "error", err)
				continue
			}

			// Read incoming packet
			n, addr, err := l.conn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					// This is just a timeout, continue
					continue
				}
				slog.Error("Error reading from UDP", "error", err)
				continue
			}

			// Process the packet in a separate goroutine
			packet := make([]byte, n)
			copy(packet, buffer[:n])
			go l.processPacket(ctx, packet, addr)
		}
	}
}

// validatePacket performs comprehensive validation of incoming SNMP packets
func (l *TrapListener) validatePacket(packet []byte, addr *net.UDPAddr) error {
	// Check packet size limits
	const (
		minPacketSize = 10   // Minimum valid SNMP packet size
		maxPacketSize = 8192 // Maximum reasonable SNMP packet size
	)

	if len(packet) < minPacketSize {
		return errors.New(errors.ValidationError, "Packet too small to be valid SNMP")
	}

	if len(packet) > maxPacketSize {
		return errors.New(errors.ValidationError, "Packet exceeds maximum size limit")
	}

	// Basic SNMP structure validation - check for valid ASN.1 BER encoding
	if err := l.validateASN1Structure(packet); err != nil {
		return errors.Wrap(err, errors.ValidationError, "Invalid ASN.1 structure")
	}

	// Try to parse the packet to validate SNMP structure
	snmp := &gosnmp.GoSNMP{
		Target:    addr.IP.String(),
		Port:      uint16(addr.Port),
		Community: l.config.SNMP.Community,
		Version:   gosnmp.Version2c, // Start with v2c for validation
		Timeout:   time.Duration(1) * time.Second,
		Retries:   0,
	}

	// Attempt to unmarshal the packet
	trapPacket, err := snmp.UnmarshalTrap(packet, false)
	if err != nil {
		// Try with different versions before failing
		versions := []gosnmp.SnmpVersion{gosnmp.Version1, gosnmp.Version3}
		for _, version := range versions {
			snmp.Version = version
			if trapPacket, err = snmp.UnmarshalTrap(packet, false); err == nil {
				break
			}
		}
		if err != nil {
			return errors.Wrap(err, errors.ValidationError, "Failed to parse as valid SNMP packet")
		}
	}

	// Validate PDU type is a trap
	if trapPacket.PDUType != gosnmp.Trap && trapPacket.PDUType != gosnmp.SNMPv2Trap {
		return errors.New(errors.ValidationError, "Packet is not an SNMP trap").
			WithMetadata("pdu_type", trapPacket.PDUType.String())
	}

	// Validate community string if configured
	if l.config.SNMP.Community != "" && trapPacket.Community != l.config.SNMP.Community {
		return errors.New(errors.ValidationError, "Community string mismatch").
			WithMetadata("expected", l.config.SNMP.Community).
			WithMetadata("received", trapPacket.Community)
	}

	// Validate variable bindings
	if err := l.validateVariableBindings(trapPacket.Variables); err != nil {
		return errors.Wrap(err, errors.ValidationError, "Invalid variable bindings")
	}

	slog.Debug("SNMP packet validation passed",
		"source", addr.String(),
		"version", trapPacket.Version.String(),
		"community", trapPacket.Community,
		"variables", len(trapPacket.Variables))

	return nil
}

// validateASN1Structure performs basic ASN.1 BER structure validation
func (l *TrapListener) validateASN1Structure(packet []byte) error {
	if len(packet) < 2 {
		return errors.New(errors.ValidationError, "Packet too short for ASN.1 header")
	}

	// Check for valid ASN.1 tag (SNMP messages start with SEQUENCE tag 0x30)
	if packet[0] != 0x30 {
		return errors.New(errors.ValidationError, "Invalid ASN.1 tag, expected SEQUENCE").
			WithMetadata("tag", fmt.Sprintf("0x%02x", packet[0]))
	}

	// Validate length encoding
	lengthByte := packet[1]
	if lengthByte&0x80 == 0 {
		// Short form length
		expectedLength := int(lengthByte)
		if len(packet) < expectedLength+2 {
			return errors.New(errors.ValidationError, "Packet shorter than declared length")
		}
	} else {
		// Long form length
		lengthOfLength := int(lengthByte & 0x7f)
		if lengthOfLength == 0 {
			return errors.New(errors.ValidationError, "Indefinite length not allowed in SNMP")
		}
		if lengthOfLength > 4 {
			return errors.New(errors.ValidationError, "Length encoding too long")
		}
		if len(packet) < 2+lengthOfLength {
			return errors.New(errors.ValidationError, "Packet too short for length encoding")
		}
	}

	return nil
}

// validateVariableBindings validates SNMP variable bindings
func (l *TrapListener) validateVariableBindings(variables []gosnmp.SnmpPDU) error {
	const maxVariables = 100 // Reasonable limit for variable bindings

	if len(variables) > maxVariables {
		return errors.New(errors.ValidationError, "Too many variable bindings").
			WithMetadata("count", len(variables)).
			WithMetadata("max_allowed", maxVariables)
	}

	for i, variable := range variables {
		// Validate OID format
		if variable.Name == "" {
			return errors.New(errors.ValidationError, "Empty OID in variable binding").
				WithMetadata("variable_index", i)
		}

		// Basic OID format validation (should start with digit or dot)
		if !strings.HasPrefix(variable.Name, ".") && !strings.HasPrefix(variable.Name, "1") {
			return errors.New(errors.ValidationError, "Invalid OID format").
				WithMetadata("oid", variable.Name).
				WithMetadata("variable_index", i)
		}

		// Validate variable type is supported
		if err := l.validateVariableType(variable.Type); err != nil {
			return errors.Wrap(err, errors.ValidationError, "Invalid variable type").
				WithMetadata("variable_index", i).
				WithMetadata("oid", variable.Name)
		}

		// Validate value is not nil for non-null types
		if variable.Type != gosnmp.Null && variable.Value == nil {
			return errors.New(errors.ValidationError, "Null value for non-null type").
				WithMetadata("variable_index", i).
				WithMetadata("oid", variable.Name).
				WithMetadata("type", variable.Type.String())
		}
	}

	return nil
}

// validateVariableType checks if the SNMP variable type is supported
func (l *TrapListener) validateVariableType(varType gosnmp.Asn1BER) error {
	supportedTypes := map[gosnmp.Asn1BER]bool{
		gosnmp.Integer:          true,
		gosnmp.OctetString:      true,
		gosnmp.ObjectIdentifier: true,
		gosnmp.IPAddress:        true,
		gosnmp.Counter32:        true,
		gosnmp.Gauge32:          true,
		gosnmp.TimeTicks:        true,
		gosnmp.Counter64:        true,
		gosnmp.Null:             true,
		gosnmp.NoSuchObject:     true,
		gosnmp.NoSuchInstance:   true,
		gosnmp.EndOfMibView:     true,
	}

	if !supportedTypes[varType] {
		return errors.New(errors.ValidationError, "Unsupported variable type").
			WithMetadata("type", varType.String()).
			WithMetadata("type_code", int(varType))
	}

	return nil
}

// tryParseWithVersions attempts to parse the packet with different SNMP versions
func (l *TrapListener) tryParseWithVersions(packet []byte, addr *net.UDPAddr) (*gosnmp.SnmpPacket, error) {
	// Try different SNMP versions in order of likelihood
	versions := []struct {
		version gosnmp.SnmpVersion
		name    string
	}{
		{gosnmp.Version2c, "2c"},
		{gosnmp.Version1, "1"},
		{gosnmp.Version3, "3"},
	}

	for _, v := range versions {
		snmp := &gosnmp.GoSNMP{
			Target:    addr.IP.String(),
			Port:      uint16(addr.Port),
			Community: l.config.SNMP.Community,
			Version:   v.version,
			Timeout:   time.Duration(2) * time.Second,
			Retries:   1,
		}

		trapPacket, err := snmp.UnmarshalTrap(packet, false)
		if err == nil {
			slog.Debug("Successfully parsed SNMP packet with auto-detected version",
				"source", addr.String(),
				"detected_version", v.name,
				"configured_version", l.config.SNMP.Version)
			return trapPacket, nil
		}

		slog.Debug("Failed to parse with version",
			"error", err,
			"source", addr.String(),
			"tried_version", v.name)
	}

	return nil, fmt.Errorf("failed to parse SNMP packet with any supported version")
}

// processPacket processes an incoming SNMP packet
func (l *TrapListener) processPacket(ctx context.Context, packet []byte, addr *net.UDPAddr) {
	// Validate input
	if len(packet) == 0 {
		slog.Error("Empty packet received")
		return
	}
	if addr == nil {
		slog.Error("Invalid address: address is nil")
		return
	}

	// Validate packet before processing
	if err := l.validatePacket(packet, addr); err != nil {
		slog.Warn("Invalid SNMP packet received",
			"source", addr.String(),
			"error", err,
			"packet_size", len(packet))
		return
	}

	// Log the received packet
	slog.Debug("Received UDP packet",
		"source", addr.String(),
		"packet_size", len(packet))

	// Create a GoSNMP instance for parsing
	snmp := &gosnmp.GoSNMP{
		Target:    addr.IP.String(),
		Port:      uint16(addr.Port),
		Community: l.config.SNMP.Community,
		Version:   gosnmp.Version2c, // Default to v2c
		Timeout:   time.Duration(2) * time.Second,
		Retries:   1,
	}

	// Set version based on configuration
	switch l.config.SNMP.Version {
	case "1":
		snmp.Version = gosnmp.Version1
	case "2c":
		snmp.Version = gosnmp.Version2c
	case "3":
		snmp.Version = gosnmp.Version3
		// For SNMPv3, we need additional security parameters
		// The UnmarshalTrap method will handle the security parameters from the packet
		slog.Debug("Processing SNMPv3 trap - security parameters will be extracted from packet",
			"source", addr.String())
	default:
		slog.Warn("Unknown SNMP version in configuration, defaulting to v2c",
			"version", l.config.SNMP.Version)
		snmp.Version = gosnmp.Version2c
	}

	// Parse the SNMP packet using gosnmp.UnmarshalTrap
	trapPacket, err := snmp.UnmarshalTrap(packet, false)
	if err != nil {
		// If parsing fails with the configured version, try auto-detection
		slog.Debug("Failed to parse with configured version, attempting auto-detection",
			"error", err,
			"source", addr.String(),
			"configured_version", l.config.SNMP.Version)

		trapPacket, err = l.tryParseWithVersions(packet, addr)
		if err != nil {
			slog.Error("Failed to unmarshal SNMP trap packet with any version",
				"error", err,
				"source", addr.String(),
				"packet_size", len(packet))
			return
		}
	}

	// Validate that we received a trap packet
	if trapPacket.PDUType != gosnmp.Trap && trapPacket.PDUType != gosnmp.SNMPv2Trap {
		slog.Warn("Received non-trap SNMP packet",
			"source", addr.String(),
			"pdu_type", trapPacket.PDUType.String())
		return
	}

	slog.Debug("Successfully parsed SNMP trap packet",
		"source", addr.String(),
		"version", trapPacket.Version.String(),
		"community", trapPacket.Community,
		"pdu_type", trapPacket.PDUType.String(),
		"variables", len(trapPacket.Variables))

	// Handle the trap
	if err := l.handler.HandleTrap(ctx, trapPacket, addr); err != nil {
		slog.Error("Failed to handle SNMP trap",
			"error", err,
			"source", addr.String())
	}
}
