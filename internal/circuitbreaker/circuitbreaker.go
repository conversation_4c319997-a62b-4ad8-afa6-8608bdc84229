package circuitbreaker

import (
	"context"
	"log/slog"
	"sync"
	"time"

	"argus/internal/errors"
)

// State represents the state of a circuit breaker
type State string

const (
	// StateClosed means the circuit is closed and requests are allowed
	StateClosed State = "closed"
	// StateOpen means the circuit is open and requests are not allowed
	StateOpen State = "open"
	// StateHalfOpen means the circuit is half-open and a limited number of requests are allowed
	StateHalfOpen State = "half-open"
)

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	name                 string
	state                State
	failureThreshold     int
	successThreshold     int
	resetTimeout         time.Duration
	halfOpenMaxRequests  int
	failureCount         int
	successCount         int
	lastStateChange      time.Time
	halfOpenRequestCount int
	mutex                sync.RWMutex
}

// Options contains options for creating a circuit breaker
type Options struct {
	Name                string
	FailureThreshold    int
	SuccessThreshold    int
	ResetTimeout        time.Duration
	HalfOpenMaxRequests int
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(options Options) (*CircuitBreaker, error) {
	// Validate options
	if options.Name == "" {
		return nil, errors.New(errors.ValidationError, "Name is required")
	}
	if options.FailureThreshold <= 0 {
		options.FailureThreshold = 5 // Default failure threshold
	}
	if options.SuccessThreshold <= 0 {
		options.SuccessThreshold = 2 // Default success threshold
	}
	if options.ResetTimeout <= 0 {
		options.ResetTimeout = 30 * time.Second // Default reset timeout
	}
	if options.HalfOpenMaxRequests <= 0 {
		options.HalfOpenMaxRequests = 1 // Default max requests in half-open state
	}

	return &CircuitBreaker{
		name:                 options.Name,
		state:                StateClosed,
		failureThreshold:     options.FailureThreshold,
		successThreshold:     options.SuccessThreshold,
		resetTimeout:         options.ResetTimeout,
		halfOpenMaxRequests:  options.HalfOpenMaxRequests,
		failureCount:         0,
		successCount:         0,
		lastStateChange:      time.Now(),
		halfOpenRequestCount: 0,
		mutex:                sync.RWMutex{},
	}, nil
}

// Execute executes a function with circuit breaker protection
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func(ctx context.Context) error) error {
	// Check if the circuit is open
	if !cb.allowRequest() {
		return errors.New(errors.CircuitBreakerError, "Circuit breaker is open").
			WithMetadata("name", cb.name).
			WithMetadata("state", string(cb.state))
	}

	// Execute the function
	err := fn(ctx)

	// Update the circuit breaker state based on the result
	if err != nil {
		cb.recordFailure()
		return err
	}

	cb.recordSuccess()
	return nil
}

// allowRequest checks if a request is allowed based on the current state
func (cb *CircuitBreaker) allowRequest() bool {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	// Check if the circuit is closed
	if cb.state == StateClosed {
		return true
	}

	// Check if the circuit is open
	if cb.state == StateOpen {
		// Check if the reset timeout has elapsed
		if time.Since(cb.lastStateChange) > cb.resetTimeout {
			// Transition to half-open state
			cb.mutex.RUnlock()
			cb.transitionToHalfOpen()
			cb.mutex.RLock()
		} else {
			return false
		}
	}

	// Check if the circuit is half-open
	if cb.state == StateHalfOpen {
		// Allow a limited number of requests in half-open state
		return cb.halfOpenRequestCount < cb.halfOpenMaxRequests
	}

	return true
}

// recordSuccess records a successful request
func (cb *CircuitBreaker) recordSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	// Reset failure count
	cb.failureCount = 0

	// Increment success count
	cb.successCount++

	// If in half-open state, increment request count
	if cb.state == StateHalfOpen {
		cb.halfOpenRequestCount++
	}

	// Check if we should transition to closed state
	if cb.state == StateHalfOpen && cb.successCount >= cb.successThreshold {
		cb.transitionToClosed()
	}
}

// recordFailure records a failed request
func (cb *CircuitBreaker) recordFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	// Reset success count
	cb.successCount = 0

	// Increment failure count
	cb.failureCount++

	// If in half-open state, increment request count
	if cb.state == StateHalfOpen {
		cb.halfOpenRequestCount++
	}

	// Check if we should transition to open state
	if (cb.state == StateClosed && cb.failureCount >= cb.failureThreshold) ||
		(cb.state == StateHalfOpen) {
		cb.transitionToOpen()
	}
}

// transitionToOpen transitions the circuit breaker to the open state
func (cb *CircuitBreaker) transitionToOpen() {
	prevState := cb.state
	cb.state = StateOpen
	cb.lastStateChange = time.Now()
	cb.failureCount = 0
	cb.successCount = 0
	cb.halfOpenRequestCount = 0

	slog.Warn("Circuit breaker transitioned to open state",
		"name", cb.name,
		"previous_state", string(prevState),
		"reset_timeout", cb.resetTimeout.String())
}

// transitionToHalfOpen transitions the circuit breaker to the half-open state
func (cb *CircuitBreaker) transitionToHalfOpen() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	prevState := cb.state
	cb.state = StateHalfOpen
	cb.lastStateChange = time.Now()
	cb.failureCount = 0
	cb.successCount = 0
	cb.halfOpenRequestCount = 0

	slog.Info("Circuit breaker transitioned to half-open state",
		"name", cb.name,
		"previous_state", string(prevState),
		"max_requests", cb.halfOpenMaxRequests)
}

// transitionToClosed transitions the circuit breaker to the closed state
func (cb *CircuitBreaker) transitionToClosed() {
	prevState := cb.state
	cb.state = StateClosed
	cb.lastStateChange = time.Now()
	cb.failureCount = 0
	cb.successCount = 0
	cb.halfOpenRequestCount = 0

	slog.Info("Circuit breaker transitioned to closed state",
		"name", cb.name,
		"previous_state", string(prevState))
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() State {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// Reset resets the circuit breaker to the closed state
func (cb *CircuitBreaker) Reset() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	prevState := cb.state
	cb.state = StateClosed
	cb.lastStateChange = time.Now()
	cb.failureCount = 0
	cb.successCount = 0
	cb.halfOpenRequestCount = 0

	slog.Info("Circuit breaker reset to closed state",
		"name", cb.name,
		"previous_state", string(prevState))
}
